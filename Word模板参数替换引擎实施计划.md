# Word模板参数替换引擎实施计划

## 项目状态：✅ 已完成代码重构

### 最新更新（当前状态）
**代码重构完成**: 所有功能代码已成功从 `com.zjhh.economy.service` 包迁移到 `com.zjhh.economy.onlyoffice.report` 包，并按照用户要求删除了所有测试文件。

---

## 一、项目概述

### 目标
在楼宇经济管理系统中实现Word模板参数替换引擎，支持基于数据库数据自动生成Word文档报告。

### 核心功能
- 解析Word模板中的参数（格式：{{业务数据名称.字段名称}}）
- 从数据库查询对应数据（ads_dms_report_data相关表）
- 支持文本、表格、图表三种参数类型
- 生成最终Word文档并保存

---

## 二、技术架构 ✅ 已完成

### 重构后的目录结构
```
com.zjhh.economy.onlyoffice.report/
├── ReportDataSourceTranslator.java      # 数据源名称翻译服务
├── ReportDataQueryService.java          # 报告数据查询服务  
├── TemplateParameterAnalyzer.java       # 模板参数分析器
├── TemplateFillDataBuilder.java         # 模板数据填充构建器
└── dto/                                 # 数据传输对象包
    ├── ChartParameterInfo.java          # 图表参数信息
    ├── TableParameterInfo.java          # 表格参数信息  
    ├── TextParameterInfo.java           # 文本参数信息
    ├── ParameterAnalysisResult.java     # 参数分析结果
    └── ChartDataResult.java             # 图表数据结果
```

### 核心技术特性 ✅ 已实现
- **智能参数解析**: 支持多种参数格式的灵活解析
- **三级异常处理**: 服务级、数据源级、字段级异常处理
- **缓存优化**: Spring @Cacheable + ConcurrentHashMap双重缓存
- **反射优化**: 动态访问data1-data100字段
- **类型安全**: 完整的DTO体系保证类型安全

---

## 三、实施阶段 ✅ 全部完成

### ~~第一阶段：数据库集成层~~ ✅ 已完成
- ✅ ReportDataSourceTranslator - 数据源名称翻译服务
- ✅ ReportDataQueryService - 数据查询服务
- ✅ ChartDataResult DTO - 图表数据结构
- ✅ 单元测试和集成测试（已删除）

### ~~第二阶段：参数处理层~~ ✅ 已完成  
- ✅ TemplateParameterAnalyzer - 参数分析器
- ✅ TemplateFillDataBuilder - 数据填充构建器
- ✅ 参数信息DTO类（TextParameterInfo, TableParameterInfo, ChartParameterInfo）
- ✅ ParameterAnalysisResult - 参数分析结果DTO
- ✅ 综合集成测试（已删除）

### ~~第三阶段：代码重构~~ ✅ 已完成
- ✅ 迁移所有功能到 onlyoffice/report 目录
- ✅ 更新包名和导入引用
- ✅ 删除旧代码和测试文件
- ✅ 更新项目文档和记忆

### 第四阶段：集成实现 📋 待实现
**目标**: 在WordTemplateEngineTest.java中实现完整的createReport()方法

**实现计划**:
```java
private void createReport() throws Exception {
    // 1. 解析模板参数
    TemplateParameter templateParams = WordTemplateEngine.parseTemplate(templatePath);
    
    // 2. 分析参数类型
    ParameterAnalysisResult analysisResult = templateParameterAnalyzer.analyzeParameters(templateParams);
    
    // 3. 构建填充数据
    TemplateFillData fillData = templateFillDataBuilder.buildFillData(reportTypeCode, analysisResult);
    
    // 4. 生成Word文档
    Document doc = WordTemplateEngine.fillTemplate(templatePath, fillData);
    
    // 5. 保存文档
    doc.save(outputPath);
}
```

---

## 实施阶段规划

### 第一阶段：核心组件开发 (1-2周)

#### 1.1 搭建基础结构 (1-2天) ✅ 已完成
- [x] 在onlyoffice目录下创建模板处理包结构：`com.zjhh.economy.onlyoffice.template`
- [x] 创建基础模型类：`TemplateParameter`、`ParameterType`、`TemplateParseResult`、`DocumentGenerationResult`
- [x] 创建异常处理类：`TemplateProcessException`
- [ ] 添加Maven依赖 (确认Aspose.Words版本) - 需后续确认

**目录结构：**
```
zjwn-web-economy/src/main/java/com/zjhh/economy/onlyoffice/
├── template/                           // 新增模板处理模块
│   ├── WordTemplateEngine.java         // 门面类
│   ├── parser/
│   │   └── WordTemplateParameterParser.java // 参数解析器
│   ├── filler/
│   │   └── WordDocumentDataFiller.java      // 数据填充器
│   ├── model/
│   │   ├── TemplateParameter.java           // 参数模型
│   │   ├── ParameterType.java               // 参数类型枚举
│   │   ├── TemplateParseResult.java         // 解析结果
│   │   └── DocumentGenerationResult.java    // 生成结果
│   └── exception/
│       └── TemplateProcessException.java     // 异常类
├── config/                             // 现有OnlyOffice配置
├── manager/                            // 现有OnlyOffice管理器
└── service/                            // 现有OnlyOffice服务
```

#### 1.2 参数解析器开发 (2-3天) ✅ 已完成
- [x] 实现`WordTemplateParameterParser`类
- [x] 开发文本参数提取：`extractTextParameters()`
- [x] 开发表格参数提取：`extractTableParameters()`  
- [x] 开发图表参数提取：`extractChartParameters()`
- [x] 实现参数去重和分类逻辑
- [x] 添加详细日志记录

**关键实现点：**
- 文本参数：`\{\{([^#/][^}]*)\}\}` 正则匹配
- 表格参数：`\{\{(\w+)\.(\w+)\}\}` 对象字段格式
- 图表参数：`\{\{(x\d+)\}\}` 序列标识格式

#### 1.3 数据填充器开发 (3-4天) ✅ 已完成
- [x] 实现`WordDocumentDataFiller`类
- [x] 开发文本参数替换：`replaceTextParameters()`
- [x] 开发表格数据处理：`processTableParameters()`
- [x] 开发图表数据处理：`processChartParameters()`
- [x] 实现数据格式化工具：`formatValue()`
- [x] 实现表格行动态克隆和插入
- [x] 实现完整图表数据替换逻辑
- [x] 添加反射机制支持对象字段访问

**关键实现点：**
- 表格行动态生成和克隆
- 图表完整数据替换
- 对象字段值反射获取

#### 1.4 门面类集成 (1天) ✅ 已完成
- [x] 实现`WordTemplateEngine`统一接口
- [x] 提供模板解析方法：`parseTemplate()`
- [x] 提供文档生成方法：`generateDocument()`
- [x] 提供一体化方法：`parseAndGenerate()`
- [x] 支持InputStream和byte[]两种输入格式
- [x] 添加Spring @Component注解便于注入
- [x] 编写完整单元测试

## 🎉 第一阶段完成总结

### ✅ 已实现功能
1. **完整的模板参数解析**：支持文本、表格、图表三种参数类型识别
2. **智能数据填充**：自动处理复杂的表格行生成和图表数据替换
3. **统一门面接口**：提供简洁易用的API，支持多种输入格式
4. **完善的单元测试**：覆盖各种场景的测试用例
5. **修复了参数解析逻辑**：基于位置识别参数类型，不依赖参数名称格式
6. **实现参数分组机制**：按表格和图表分组管理参数，支持多表格多图表场景
7. **优化参数模型设计**：简化TemplateParameter结构，保留核心功能
8. **支持数据源解析**：自动分离数据源和字段名，优化数据绑定能力

### ✅ 新增实现功能（数据填充增强）
9. **数据填充模型设计**：为文本、表格、图表创建专门的数据填充模型类 ✅ 已完成
10. **数据填充器适配**：基于新的参数解析结果和数据模型进行精确数据绑定和填充 ✅ 已完成

### ✅ Model类系统实施完成 (2024-01-15)
11. **TextFillData模型类**：文本填充数据模型，支持数据源绑定和字段验证 ✅ 已完成
12. **TableFillData模型类**：表格填充数据模型，支持行数据管理和完整性验证 ✅ 已完成
13. **ChartSeries模型类**：图表序列模型，支持数据统计和验证 ✅ 已完成
14. **ChartFillData模型类**：图表填充数据模型，支持完整图表数据结构 ✅ 已完成
15. **TemplateFillData容器类**：统一数据容器，支持类型安全管理和向下兼容 ✅ 已完成
16. **API接口增强**：WordTemplateEngine和WordDocumentDataFiller支持Model类接口 ✅ 已完成
17. **单元测试更新**：覆盖Model类功能、数据验证和兼容性测试 ✅ 已完成

### 📁 已创建文件
- **基础模型类**: `ParameterType`, `TemplateParameter`(支持数据源), `TemplateParseResult`, `DocumentGenerationResult`
- **Model类系统**: `TextFillData`, `TableFillData`, `ChartSeries`, `ChartFillData`, `TemplateFillData`
- **核心组件**: `WordTemplateParameterParser`(支持数据源解析), `WordDocumentDataFiller`(支持Model类填充), `WordTemplateEngine`(完整API)
- **异常处理**: `TemplateProcessException`
- **单元测试**: `WordTemplateEngineTest`(包含Model类测试)

### 🔧 核心技术特性
- **基于位置的参数识别**：根据参数在文档中的上下文位置确定类型，而非参数名称格式
- **智能参数分组**：
  - 文本参数组：每个文本参数独立分组（text_param_1, text_param_2, ...）
  - 表格参数组：每个表格独立分组（table_1, table_2, ...）
  - 图表参数组：每个图表独立分组（chart_1, chart_2, ...）
- **多表格多图表支持**：支持文档中任意数量的表格和图表，避免参数混乱
- **精确数据绑定**：通过groupName实现参数组与数据源的精确匹配
- **数据源智能解析**：自动分离`数据源.字段`格式，支持结构化数据绑定
- **简洁模型设计**：TemplateParameter结构清晰，支持dataSource和fieldNames分离
- **三重数据填充接口**：
  - `fillData()`: 向后兼容的原始数据填充接口  
  - `fillDataWithParameters()`: 基于参数结构的增强数据填充接口
  - `fillDataWithModel()`: 基于Model类的类型安全填充接口
- **智能组件定位**：精确定位文档中的表格和图表组件进行数据绑定
- **动态表格行生成**：支持任意数量数据行的插入
- **完整图表替换**：支持类别和序列数据的完整替换
- **反射字段访问**：支持Java对象和Map两种数据格式
- **数据格式化**：数字、日期等常见类型的自动格式化

### 📈 测试覆盖情况
- ✅ 简单文本参数替换测试
- ✅ 复杂表格数据生成测试  
- ✅ 图表数据完整替换测试
- ✅ 综合场景集成测试
- ✅ 异常情况处理测试

### 第二阶段：数据填充适配与优化 (3-5天)

#### 2.1 数据填充器适配 (2-3天) ✅ 已完成
- [x] 新增基于TemplateParameter的fillData接口
- [x] 实现数据源绑定和字段提取逻辑
- [x] 重构文本参数组处理器
- [x] 重构表格参数组处理器
- [x] 重构图表参数组处理器
- [x] 实现组件精确定位机制
- [x] 保持向后兼容性

#### 2.2 核心功能测试 (1天)
- [ ] 测试基于数据源的参数绑定
- [ ] 测试多数据源场景
- [ ] 测试参数组精确定位
- [ ] 验证向后兼容性

**新的数据结构示例：**
```java
// 基于数据源的数据结构（已支持）
Map<String, Object> data = Map.of(
    "数据源1", List.of(
        Map.of("字段1", "产品A", "字段2", 150000, "字段3", "12%"),
        Map.of("字段1", "产品B", "字段2", 89000, "字段3", "8%")
    ),
    "数据源2", Map.of(
        "字段1", "2024年销售报告",
        "字段2", "2024-01-15"
    ),
    "数据源3", Map.of(
        "categories", Arrays.asList("Q1", "Q2", "Q3", "Q4"),
        "series", Arrays.asList(
            Map.of("name", "销售额", "values", Arrays.asList(10000.0, 12000.0, 11000.0, 15000.0)),
            Map.of("name", "利润", "values", Arrays.asList(2000.0, 2400.0, 2200.0, 3000.0))
        )
    )
);

// 使用新的API接口
DocumentGenerationResult result = wordTemplateEngine.parseAndGenerateWithParameters(templateBytes, data);
```

#### 2.3 性能优化 (1天)
- [ ] 优化组件定位算法性能
- [ ] 减少文档遍历次数
- [ ] 添加内存使用监控
- [ ] 优化异常处理机制

#### 2.4 错误处理完善 (1天)
- [ ] 完善数据源绑定异常处理
- [ ] 添加参数组定位失败处理
- [ ] 实现优雅降级处理
- [ ] 添加详细日志记录

#### 2.5 边界情况测试 (1天)  
- [ ] 测试数据源不存在的处理
- [ ] 测试字段缺失的处理
- [ ] 测试大数据量多数据源处理
- [ ] 测试混合参数格式处理

### 第三阶段：业务集成 (2-3天)

#### 3.1 Service层集成 (1天)
- [ ] 创建`DocumentTemplateService`业务服务类
- [ ] 实现模板管理功能
- [ ] 实现数据查询和组装功能
- [ ] 添加缓存机制

**Service示例：**
```java
@Service
public class DocumentTemplateService {
    
    private final WordTemplateEngine templateEngine;
    
    public byte[] generateSalesReport(SalesReportRequest request) {
        // 1. 解析模板参数
        TemplateParseResult parseResult = templateEngine.parseTemplate(templateInputStream);
        
        // 2. 根据参数查询数据
        Map<String, Object> data = buildDataFromParameters(parseResult.getParameters());
        
        // 3. 生成文档
        return templateEngine.generateDocument(templateInputStream, data);
    }
    
    private Map<String, Object> buildDataFromParameters(List<TemplateParameter> parameters) {
        // 根据参数类型分别查询数据库
        // 文本参数、表格参数、图表参数的数据组装逻辑
    }
}
```

#### 3.2 Controller接口开发 (1天)
- [ ] 创建`DocumentController`控制器
- [ ] 实现模板上传接口
- [ ] 实现文档生成接口
- [ ] 添加接口文档和参数验证

#### 3.3 前端集成测试 (1天)
- [ ] 测试模板上传功能
- [ ] 测试参数识别展示
- [ ] 测试文档生成下载
- [ ] 验证完整业务流程

### 第四阶段：部署与文档 (1-2天)

#### 4.1 部署配置 (0.5天)
- [ ] 配置生产环境参数
- [ ] 添加监控告警
- [ ] 配置日志输出

#### 4.2 使用文档编写 (1天)
- [ ] 编写API使用文档
- [ ] 编写模板设计规范
- [ ] 编写常见问题说明
- [ ] 录制使用演示视频

#### 4.3 培训交付 (0.5天)
- [ ] 开发人员技术培训
- [ ] 业务人员使用培训
- [ ] 运维人员部署培训

## 关键风险与应对

### 技术风险
1. **Aspose.Words API兼容性**
   - 风险：不同版本API差异
   - 应对：提前验证关键API，准备兼容性方案

2. **大文档性能问题**
   - 风险：处理大型模板文档时内存溢出
   - 应对：实施流式处理，添加内存监控

3. **复杂图表处理**
   - 风险：特殊图表类型处理失败
   - 应对：先支持常用图表类型，逐步扩展

### 业务风险
1. **模板格式不规范**
   - 风险：用户创建的模板格式不符合要求
   - 应对：提供模板设计规范和验证工具

2. **数据查询性能**
   - 风险：根据参数查询大量数据导致超时
   - 应对：实施查询优化和分页处理

## 测试策略

### 单元测试 (覆盖率 > 80%)
- [ ] 参数解析器测试
- [ ] 数据填充器测试
- [ ] 工具方法测试

### 集成测试
- [ ] 端到端功能测试
- [ ] 性能基准测试
- [ ] 并发安全测试

### 业务测试
- [ ] 真实模板测试
- [ ] 用户场景测试
- [ ] 兼容性测试

## 验收标准

### 功能验收
- [ ] 支持三种参数类型的识别和处理
- [ ] 能够处理复杂的表格和图表结构
- [ ] 生成的文档格式完整，样式保持

### 性能验收
- [ ] 单个文档生成时间 < 10秒
- [ ] 支持并发处理 ≥ 10个请求
- [ ] 内存使用合理，无内存泄漏

### 质量验收
- [ ] 代码质量符合团队规范
- [ ] 单元测试覆盖率 > 80%
- [ ] 无严重安全漏洞

## 资源需求

### 人力资源
- **开发人员**: 1人，4周
- **测试人员**: 0.5人，1周
- **产品对接**: 0.2人，持续

### 技术资源
- **开发环境**: JDK 21 + Maven + IDE
- **Aspose.Words许可证**: 确认当前许可证支持情况
- **测试环境**: 与生产环境一致的配置

## 时间计划

```
周次  阶段                     主要任务                      里程碑
W1    第一阶段(1-2)           基础结构+参数解析器            参数解析功能完成
W2    第一阶段(3-4)           数据填充器+门面类集成          核心引擎完成
W3    第二阶段                功能验证与优化                 功能测试通过
W4    第三阶段+第四阶段       业务集成+部署文档              整体交付完成
```

## 后续迭代规划

### V1.1 增强功能
- [ ] 支持更多图表类型
- [ ] 添加模板验证工具
- [ ] 支持条件参数处理

### V1.2 性能优化
- [ ] 实施模板缓存机制
- [ ] 优化大数据量处理
- [ ] 添加分布式处理支持

### V1.3 易用性提升
- [ ] 可视化模板设计器
- [ ] 参数映射配置界面
- [ ] 批量文档生成功能

---

**项目负责人**: [项目负责人姓名]  
**技术负责人**: [技术负责人姓名]  
**创建时间**: 2024-01-15  
**最后更新**: 2024-01-15  
**状态**: 待启动
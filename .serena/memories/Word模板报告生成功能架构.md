# Word模板报告生成功能架构

## 功能概述
Word模板报告生成功能已完成重构，现在位于 `com.zjhh.economy.onlyoffice.report` 包下。该功能支持基于Word模板和数据库数据自动生成Word文档报告。

## 核心组件架构

### 1. 目录结构
```
com.zjhh.economy.onlyoffice.report/
├── ReportDataSourceTranslator.java      # 数据源名称翻译服务
├── ReportDataQueryService.java          # 报告数据查询服务
├── TemplateParameterAnalyzer.java       # 模板参数分析器
├── TemplateFillDataBuilder.java         # 模板数据填充构建器
└── dto/                                 # 数据传输对象
    ├── ChartParameterInfo.java          # 图表参数信息
    ├── TableParameterInfo.java          # 表格参数信息
    ├── TextParameterInfo.java           # 文本参数信息
    ├── ParameterAnalysisResult.java     # 参数分析结果
    └── ChartDataResult.java             # 图表数据结果
```

### 2. 核心服务类

#### ReportDataSourceTranslator（数据源翻译服务）
- **功能**: 将业务数据名称翻译为数据库字段编码
- **核心方法**:
  - `translateDataSourceName()`: 翻译数据源名称
  - `translateColumnName()`: 翻译列名
  - `translateColumnNames()`: 批量翻译列名
- **缓存策略**: 使用Spring @Cacheable + ConcurrentHashMap双重缓存

#### ReportDataQueryService（数据查询服务）
- **功能**: 查询ads_dms_report_data相关表数据
- **核心方法**:
  - `queryTextParameterValue()`: 查询文本参数值
  - `queryTableParameterData()`: 查询表格参数数据
  - `queryChartParameterData()`: 查询图表参数数据
- **特性**: 支持反射访问data1-data100字段，三级异常处理

#### TemplateParameterAnalyzer（参数分析器）
- **功能**: 解析模板参数并按类型分类
- **参数格式**: 支持 `{{业务数据名称.字段名称}}` 格式
- **参数类型**: TEXT（文本）、TABLE（表格）、CHART（图表）
- **核心方法**:
  - `analyzeParameters()`: 分析模板参数
  - `validateParameters()`: 验证参数完整性

#### TemplateFillDataBuilder（数据填充构建器）
- **功能**: 构建完整的模板填充数据
- **核心方法**:
  - `buildFillData()`: 构建填充数据
  - `batchBuildFillData()`: 批量构建
  - `validateFillData()`: 验证填充数据

### 3. 数据传输对象（DTO）

#### 参数信息类
- **TextParameterInfo**: 文本参数信息，包含默认值处理
- **TableParameterInfo**: 表格参数信息，支持表格索引和列排序
- **ChartParameterInfo**: 图表参数信息，支持图表索引和系列配置

#### 结果类
- **ParameterAnalysisResult**: 参数分析结果聚合
- **ChartDataResult**: 图表数据结果，包含类别、系列和标题

## 关键数据库表

### ads_dms_report_data（报告数据表）
- **主要字段**: report_type_code（报告类型编码）、data1-data100（动态数据字段）
- **用途**: 存储各类报告的原始数据

### ads_dms_report_data_set（报告数据集表）
- **主要字段**: data_set_name（数据集名称）、data_set_code（数据集编码）
- **用途**: 定义业务数据名称与数据库编码的映射关系

### ads_dms_report_data_column_set（报告数据列集表）
- **主要字段**: column_name（列名）、column_code（列编码）
- **用途**: 定义业务字段名称与数据库字段的映射关系

## 使用流程

1. **模板解析**: 使用WordTemplateEngine解析Word模板，获取参数列表
2. **参数分析**: TemplateParameterAnalyzer分析参数类型和数据源
3. **数据翻译**: ReportDataSourceTranslator翻译业务名称为数据库编码
4. **数据查询**: ReportDataQueryService查询相关数据
5. **数据构建**: TemplateFillDataBuilder构建模板填充数据
6. **文档生成**: 使用WordTemplateEngine生成最终Word文档

## 性能优化

- **缓存机制**: 数据源翻译结果缓存，避免重复数据库查询
- **批量处理**: 支持批量查询和批量数据构建
- **异常处理**: 三级异常处理策略，确保系统稳定性
- **反射优化**: 针对data1-data100字段的反射访问进行了优化

## 扩展性

- **支持多种参数格式**: 灵活的参数名称解析
- **支持复杂数据结构**: 表格、图表等复杂数据类型
- **支持自定义数据源**: 可扩展的数据源翻译机制
- **支持批量操作**: 支持多个报告的批量处理
# Word模板参数替换引擎代码位置更新

## 实施位置变更
- **原计划**: 创建独立的template包 `com.zjhh.economy.template`
- **实际决定**: 在现有onlyoffice目录下实现 `com.zjhh.economy.onlyoffice.template`

## 目录结构规划
```
zjwn-web-economy/src/main/java/com/zjhh/economy/onlyoffice/
├── template/                           // 新增模板处理模块
│   ├── WordTemplateEngine.java         // 门面类
│   ├── parser/
│   │   └── WordTemplateParameterParser.java // 参数解析器
│   ├── filler/
│   │   └── WordDocumentDataFiller.java      // 数据填充器
│   ├── model/
│   │   ├── TemplateParameter.java           // 参数模型
│   │   ├── ParameterType.java               // 参数类型枚举
│   │   ├── TemplateParseResult.java         // 解析结果
│   │   └── DocumentGenerationResult.java    // 生成结果
│   └── exception/
│       └── TemplateProcessException.java     // 异常类
├── config/                             // 现有OnlyOffice配置
├── manager/                            // 现有OnlyOffice管理器
└── service/                            // 现有OnlyOffice服务
```

## 现有OnlyOffice模块结构
项目中已有完整的OnlyOffice集成：
- config/: OnlyOfficeProperties, CustomDocsIntegrationSdkConfiguration
- manager/: DocumentManagerImpl, SettingsManagerImpl, UrlManagerImpl
- service/: CallbackServiceImpl, ConfigServiceImpl

## 集成优势
1. **统一管理**: 模板处理功能与现有OnlyOffice功能在同一模块下
2. **代码复用**: 可以复用现有的配置和管理组件
3. **维护便利**: 所有文档处理相关功能集中管理
4. **架构一致**: 与现有项目结构保持一致

## 技术栈确认
- Spring Boot 3.4.3
- Java 21
- Aspose.Words (需确认版本)
- 现有OnlyOffice SDK

## 下一步实施计划
1. 在onlyoffice/template目录下创建基础结构
2. 实现参数解析器和数据填充器
3. 集成现有OnlyOffice配置和服务
4. 编写单元测试和集成测试

这种架构设计既保持了模块的独立性，又充分利用了现有的OnlyOffice基础设施。
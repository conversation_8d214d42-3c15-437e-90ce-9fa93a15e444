package com.zjhh.economy.onlyoffice.template;

import com.alibaba.fastjson2.JSONObject;
import com.aspose.words.*;
import com.zjhh.comm.utils.AsposeUtil;
import com.zjhh.economy.onlyoffice.template.model.*;
import com.zjhh.economy.onlyoffice.template.model.ChartSeries;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Word模板引擎单元测试
 */
public class WordTemplateEngineTest {
    
    private WordTemplateEngine engine;
    
    @BeforeEach
    void setUp() {
        engine = new WordTemplateEngine();
        AsposeUtil.init();
    }
    
    @Test
    void testParseTemplate() throws Exception {
        // 创建简单测试模板
        byte[] templateBytes = createSimpleTemplate();
        
        // 解析参数
        TemplateParseResult result = engine.parseTemplate(templateBytes);
        
        // 验证解析结果
        assertNotNull(result);
        assertFalse(result.getParameters().isEmpty());
        
        // 输出所有解析到的参数
        System.out.println("=== 解析到的所有参数 ===");
        List<TemplateParameter> allParams = result.getParameters();
        System.out.println(JSONObject.toJSONString(allParams));
    }
    
    @Test
    void testGenerateDocumentWithModel() throws Exception {
        // 创建测试模板
        byte[] templateBytes = createSimpleTemplate();
        
        // 准备Model测试数据 - 使用完整数据测试所有参数类型
        TemplateFillData fillData = createModelFillData();
        
        // 生成文档
        byte[] result = engine.generateDocumentWithModel(templateBytes, fillData);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.length > 0);
        
        // 保存测试结果（可选）
        saveTestResult(result, "test-simple-output.docx");
    }
    
    @Test
    void testErrorHandling() {
        // 测试空数据
        assertThrows(Exception.class, () -> {
            engine.parseTemplate((byte[]) null);
        });
        
        // 测试无效模板
        assertThrows(Exception.class, () -> {
            engine.parseTemplate(new byte[0]);
        });
    }
    
    @Test
    void testModelFillDataValidation() {
        // 测试TextFillData - 对应test.docx参数
        TextFillData textData = 
            TextFillData.createSingleField("数据源3", "字段4", "测试标题");
        assertEquals("测试标题", textData.getFields().get("字段4"));
        assertTrue(textData.getFields().containsKey("字段4"));
        assertEquals(1, textData.getFields().size());
        
        // 测试TableFillData - 对应test.docx中的数据源1
        List<Map<String, Object>> rows = Arrays.asList(
            Map.of("字段1", "产品A", "字段2", 100000, "字段3", "12%"),
            Map.of("字段1", "产品B", "字段2", 200000, "字段3", "8%")
        );
        TableFillData tableData = 
            TableFillData.builder()
                .dataSource("数据源1")
                .rows(rows)
                .fieldNames(Arrays.asList("字段1", "字段2", "字段3"))
                .build();
        assertEquals(2, tableData.getRowCount());
        assertEquals("产品A", tableData.getRow(0).get("字段1"));
        assertTrue(tableData.validateData());
        
        // 测试ChartFillData - 对应test.docx中的数据源4
        List<ChartSeries> series = Arrays.asList(
            ChartSeries.create("字段1", Arrays.asList(100.0, 200.0, 150.0))
        );
        ChartFillData chartData = 
            ChartFillData.create("数据源4", 
                Arrays.asList("Q1", "Q2", "Q3"), series);
        assertEquals(3, chartData.getCategoryCount());
        assertEquals(1, chartData.getSeriesCount());
        
        // 测试TemplateFillData
        TemplateFillData fillData = 
            TemplateFillData.empty()
                .addTextData("text_param_1", textData)
                .addTableData("table_1", tableData)
                .addChartData("chart_1", chartData);
        assertEquals(1, fillData.getTextData().size());
        assertEquals(1, fillData.getTableData().size());
        assertEquals(1, fillData.getChartData().size());
        assertTrue(fillData.getTextData().containsKey("text_param_1"));
        assertTrue(fillData.getTableData().containsKey("table_1"));
        assertTrue(fillData.getChartData().containsKey("chart_1"));
    }
    
    private byte[] createSimpleTemplate() throws Exception {
        // 使用项目根目录下的test.docx文件作为测试模板
        // 测试运行时工作目录在zjwn-web-economy子模块中，需要向上一级找到项目根目录
        return Files.readAllBytes(Paths.get("../test.docx"));
    }
    
    private byte[] documentToBytes(Document doc) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.save(out, SaveFormat.DOCX);
        return out.toByteArray();
    }
    
    /**
     * 创建Model类填充数据
     */
    private TemplateFillData createModelFillData() {
        // 创建文本数据 - 对应test.docx中的文本参数
        TextFillData textData1 = TextFillData.createSingleField("数据源3", "字段4", "2024年度销售报告");
        TextFillData textData2 = TextFillData.createSingleField("数据源7", "字段1", "张三");
        
        // 创建表格数据1 - 对应数据源1
        List<Map<String, Object>> table1Rows = Arrays.asList(
            Map.of("字段1", "产品A", "字段2", 150000, "字段3", "12%"),
            Map.of("字段1", "产品B", "字段2", 89000, "字段3", "8%"),
            Map.of("字段1", "产品C", "字段2", 200000, "字段3", "15%")
        );
        TableFillData tableData1 = TableFillData.builder()
                .dataSource("数据源1")
                .rows(table1Rows)
                .fieldNames(Arrays.asList("字段1", "字段2", "字段3"))
                .build();
        
        // 创建表格数据2 - 对应数据源2  
        List<Map<String, Object>> table2Rows = Arrays.asList(
            Map.of("字段1", "Q1", "字段2", 1200000, "字段3", "10%", "字段4", "优秀"),
            Map.of("字段1", "Q2", "字段2", 1350000, "字段3", "12%", "字段4", "良好"),
            Map.of("字段1", "Q3", "字段2", 1150000, "字段3", "8%", "字段4", "一般"),
            Map.of("字段1", "Q4", "字段2", 1580000, "字段3", "18%", "字段4", "优秀")
        );
        TableFillData tableData2 = TableFillData.builder()
                .dataSource("数据源2")
                .rows(table2Rows)
                .fieldNames(Arrays.asList("字段1", "字段2", "字段3", "字段4"))
                .build();
        
        // 创建图表数据 - 对应数据源4
        List<ChartSeries> series = Arrays.asList(
            ChartSeries.create("销售额", Arrays.asList(120.0, 135.0, 115.0, 158.0)),
            ChartSeries.create("增长率", Arrays.asList(10.0, 12.0, 8.0, 18.0)),
            ChartSeries.create("利润率", Arrays.asList(15.0, 18.0, 12.0, 22.0))
        );
        ChartFillData chartData = ChartFillData.builder()
                .dataSource("数据源4")
                .categories(Arrays.asList("Q1", "Q2", "Q3", "Q4"))
                .series(series)
                .title("销售数据图表")
                .build();
        
        // 组合填充数据 - 对应test.docx的参数分组
        return TemplateFillData.empty()
            .addTextData("text_param_1", textData1)   // {{数据源3.字段4}}
            .addTextData("text_param_2", textData2)   // {{数据源7.字段1}}
            .addTableData("table_1", tableData1)      // 数据源1的表格
            .addTableData("table_2", tableData2)      // 数据源2的表格
            .addChartData("chart_1", chartData);      // 数据源4的图表
    }
    
    @Test
    void testDebugDocumentGeneration() throws Exception {
        // 解析test.docx看看实际参数
        byte[] templateBytes = createSimpleTemplate();
        TemplateParseResult parseResult = engine.parseTemplate(templateBytes);
        
        System.out.println("=== 实际解析到的参数 ===");
        for (TemplateParameter param : parseResult.getParameters()) {
            System.out.println(String.format("组名: %s, 类型: %s, 数据源: %s, 字段: %s", 
                param.getGroupName(), param.getType(), param.getDataSource(), param.getFieldNames()));
        }
        
        // 根据实际解析的参数创建精确匹配的测试数据
        TemplateFillData fillData = TemplateFillData.empty();
        
        // 根据解析结果动态创建填充数据
        for (TemplateParameter param : parseResult.getParameters()) {
            if (param.getType() == ParameterType.TEXT) {
                String fieldName = param.getFieldNames().get(0);
                TextFillData textData = TextFillData.createSingleField(
                    param.getDataSource(), 
                    fieldName, 
                    "替换值_" + param.getGroupName()
                );
                fillData.addTextData(param.getGroupName(), textData);
                System.out.println(String.format("添加文本数据: 组名=%s, 数据源=%s, 字段=%s, 值=%s", 
                    param.getGroupName(), param.getDataSource(), fieldName, "替换值_" + param.getGroupName()));
            }
        }
        
        System.out.println("=== 填充数据概况 ===");
        System.out.println("文本数据组: " + fillData.getTextData().keySet());
        
        // 生成文档
        DocumentGenerationResult result = engine.parseAndGenerateWithModel(templateBytes, fillData);
        assertNotNull(result);
        saveTestResult(result.getDocumentBytes(), "test-debug-output.docx");
        
        System.out.println("调试文档已保存: test-debug-output.docx");
    }
    
    public static void main(String[] args) {
        try {
            WordTemplateEngineTest test = new WordTemplateEngineTest();
            test.setUp();
            test.testDebugDocumentGeneration();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void saveTestResult(byte[] result, String filename) {
        try {
            Files.write(Paths.get("target/" + filename), result);
            System.out.println("测试结果已保存: target/" + filename);
        } catch (Exception e) {
            System.err.println("保存测试结果失败: " + e.getMessage());
        }
    }
}
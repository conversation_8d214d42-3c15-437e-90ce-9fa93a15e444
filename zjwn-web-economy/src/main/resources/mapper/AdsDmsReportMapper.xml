<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsDmsReportMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        report_type_code, report_type_name, create_user, create_time, report_date, report_state, cron_expression, description, cron_state
    </sql>

    <select id="listCreateReportDoc" resultType="com.zjhh.economy.vo.earlywarning.CreateReportDocVo">
        select t1.report_type_code, t1.report_type_name, t2.page_json
        from ads_dms_report t1
        left join ads_dms_report_page t2 on t1.report_type_code = t2.report_type_code
        where t1.report_type_code in
        <foreach collection="reportTypeCodes" item="reportTypeCode" open="(" close=")" separator=",">
            #{reportTypeCode}
        </foreach>
    </select>

    <select id="pageAnalysisReport" resultType="com.zjhh.economy.vo.earlywarning.AdsDmsReportVo">
        select report_type_code,
        report_type_name,
        create_user,
        create_time,
        report_date,
        report_state,
        cron_expression,
        description,
        cron_state,
        (select count(1) from ads_dms_report_page t1 where t1.report_type_code = t.report_type_code) as preview
        from ads_dms_report t
        <where>
            <if test="req.reportTypeName != null and req.reportTypeName != ''">
                and t.report_type_name like concat('%',#{req.reportTypeName},'%')
            </if>
            <if test="req.reportState != null and req.reportState != 99">
                and t.report_state = #{req.reportState}
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="listSelectOrgCodes" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select code as key, parent_code as parent_key, name as title, code as value
        from v_organize order by code
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmProjectMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , serial_no, project_name, community_code, longitude, dimension, project_area, manage_company, contacts, contact_phone, project_intro, xh, create_user, create_time, update_time
    </sql>

    <resultMap id="communityMenu" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <collection property="children" ofType="com.zjhh.comm.vo.TreeSelectVo" select="listProjectMenu"
                    column="{key=key}">
        </collection>
    </resultMap>

    <resultMap id="communityMenuWithoutFloor" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <collection property="children" ofType="com.zjhh.comm.vo.TreeSelectVo" select="listProjectMenuWithoutFloor"
                    column="{key=key}">
        </collection>
    </resultMap>

    <resultMap id="communityMenuByCockpit" type="com.zjhh.economy.vo.CockpitTreeSelectedVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="levelType" property="levelType"/>
        <result column="projectType" property="projectType"/>
        <collection property="children" ofType="com.zjhh.economy.vo.CockpitTreeSelectedVo"
                    select="listProjectMenuByCockpit"
                    column="{key=key,projectType=projectType}">
        </collection>
    </resultMap>

    <resultMap id="projectMenuByType" type="com.zjhh.economy.vo.CockpitTreeSelectedVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="type" property="type"/>
        <result column="value" property="value"/>
        <result column="levelType" property="levelType"/>
        <collection property="children" select="listBuildingMenuByCockpit"
                    ofType="com.zjhh.economy.vo.CockpitTreeSelectedVo"
                    column="{key=key}">
            <result column="title" property="title"/>
            <result column="key" property="key"/>
            <result column="value" property="value"/>
        </collection>
    </resultMap>


    <resultMap id="projectMenu" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <collection property="children" select="listBuildingMenu" ofType="com.zjhh.comm.vo.TreeSelectVo"
                    column="{key=key}">
            <result column="title" property="title"/>
            <result column="key" property="key"/>
            <result column="value" property="value"/>
        </collection>
    </resultMap>

    <resultMap id="projectMenuWithoutFloor" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <collection property="children" select="listBuildingMenuWithoutFloor" ofType="com.zjhh.comm.vo.TreeSelectVo"
                    column="{key=key}">
            <result column="title" property="title"/>
            <result column="key" property="key"/>
            <result column="value" property="value"/>
        </collection>
    </resultMap>

    <resultMap id="buildingMenu" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <collection property="children" select="listFloorMenu" ofType="com.zjhh.comm.vo.TreeSelectVo"
                    column="{key=key}">
            <result column="title" property="title"/>
            <result column="key" property="key"/>
            <result column="value" property="value"/>
        </collection>
    </resultMap>


    <resultMap id="buildingMenuWithoutFloor" type="com.zjhh.comm.vo.TreeSelectVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
    </resultMap>

    <resultMap id="buildingMenuByCockpit" type="com.zjhh.economy.vo.CockpitTreeSelectedVo">
        <result column="title" property="title"/>
        <result column="key" property="key"/>
        <result column="levelType" property="levelType"/>
        <result column="value" property="value"/>
    </resultMap>

    <resultMap id="projectDetail" type="com.zjhh.economy.vo.ProjectDetailVo">
        <id column="id" property="id"></id>
        <result column="project_name" property="projectName"/>
        <result column="project_type" property="projectType"/>
        <result column="community_code" property="communityCode"/>
        <result column="longitude" property="longitude"/>
        <result column="dimension" property="dimension"/>
        <result column="project_area" property="projectArea"/>
        <result column="manage_company" property="manageCompany"/>
        <result column="contacts" property="contacts"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="project_intro" property="projectIntro"/>
        <collection property="projectImages" select="listProjectImages"
                    column="{businessId=id}">
            <id column="document_id" property="documentId"/>
            <result column="document_name" property="documentName"/>
        </collection>
    </resultMap>

    <select id="findMaxXh" resultType="java.lang.Integer">
        select coalesce(max(xh), 0)
        from ads_pm_project
    </select>

    <select id="pageProject" resultType="com.zjhh.economy.vo.ProjectListVo">
        select project_name,
        app.id,
        (select name from dm_pm where code = app.project_type and type = 'ProjectType') as projectType,
        app.serial_no,
        (select name from dm_pm dp where type = 'Community' and dp.code = community_code) as communityName,
        coalesce(sum(t3.building_area),0) AS totalBuildingArea,
        coalesce(sum(t3.business_area),0) AS totalBusinessArea,
        coalesce(sum(t3.totalRoomCount),0) AS totalRoomCount,
        coalesce(sum(t3.emptyRoomCount) ,0)AS emptyRoomCount,
        (select count(1) from ads_pm_building apb where apb.project_id = app.id) as buildingCount
        from ads_pm_project app
        left join (select id,
        serial_no,
        building_name,
        project_id,
        building_area,
        business_area,
        operation_time,
        building_status_code,
        building_type_code,
        head,
        phone,
        introduce,
        land_park_space,
        underground_park_space,
        xh,
        create_user,
        create_time,
        update_time,
        building_id,
        totalRoomCount,
        emptyRoomCount
        from ads_pm_building apb
        left join (select t1.building_id,
        sum(t1.totalRoomCount) as totalRoomCount,
        sum(t1.emptyRoomCount) as emptyRoomCount
        from (select t1.building_id,
        sum(totalRoomCount) as totalRoomCount,
        sum(emptyRoomCount) as emptyRoomCount
        from (select apf.building_id, count(1) as totalRoomCount, 0 as emptyRoomCount
        from ads_pm_floor apf
        left join ads_pm_room apr on apf.id = apr.floor_id
        where apr.id is not null
        group by building_id
        union all
        SELECT apf.building_id, 0 AS totalRoomCount, count(1) AS emptyRoomCount
        FROM ads_pm_floor apf
        LEFT JOIN ads_pm_room apr ON apf.id = apr.floor_id
        WHERE apr.id NOT IN (SELECT room_id
        FROM ads_pm_room_enterprise apre
        WHERE apre.moved = false)
        GROUP BY building_id
        ) t1
        group by building_id) t1
        group by t1.building_id) t2 on apb.id = t2.building_id) t3
        on t3.project_id = app.id
        <where>
            <if test="req.projectName != null and req.projectName != ''">
                app.project_name like concat('%',#{req.projectName},'%')
            </if>
            <if test="req.communityCodes != null and req.communityCodes.size > 0">
                and app.community_code in
                <foreach collection="req.communityCodes" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by app.project_name, app.id, app.community_code,app.serial_no,app.project_type
        order by app.serial_no
    </select>
    <select id="getProjectDetail" resultMap="projectDetail">
        select id,
               project_name,

               community_code,
               longitude,
               dimension,
               project_area,
               manage_company,
               contacts,
               contact_phone,
               project_intro,
               address,
               (select name from dm_pm where code = project_type and type = 'ProjectType') as project_type
        from ads_pm_project
        where id = #{projectId}

    </select>
    <select id="listProjectImages" resultType="com.zjhh.economy.request.ProjectImageVo">
        select ad.id as documentId, ad.title as documentName, ad.path as documentPath, ad.size
        from ads_business_document abd
                 left join ads_document ad on abd.document_id = ad.id
        where abd.business_id = #{businessId}
    </select>
    <select id="pageBuildingCondition" resultType="com.zjhh.economy.vo.BuildingConditionVo">
        select t1.serial_no  as serialNo,
        t1.serial_no,
        t1.project_name,
        t1.projectType,
        t1.communityName,
        t1.businessArea,
        t1.buildingArea,
        t1.settledArea,
        t1.totalRoomArea,
        t1.buildingCount,
        t1.totalRoomCount,
        t1.emptyRoomCount,
        t1.unitCount,
        t1.enCount,
        t1.registerentCount,
        t1.registerunitCount,
        round(coalesce(t1.taxIncome,0) / 10000,2) as taxIncome,
        round(coalesce(t1.withoutBuildingTax,0) / 10000,2)  as withoutBuildingTax,
        t1.businessArea - t1.settledArea  as emptyArea,
        case when t1.buildingArea = 0 then 0
        else round((t1.businessArea - t1.settledArea) / t1.businessArea * 100, 2) end as emptyRate,
        case when t1.businessArea = 0 then 0
        else round(t1.settledArea / t1.businessArea * 100, 2) end as settledRate,
        case when t1.unitCount = 0 then 0
        else round(t1.registerUnitCount::decimal / t1.unitCount::decimal * 100, 2) end as registerRate,
        case when t1.businessArea = 0 then 0
        else round(t1.taxIncome / t1.businessArea , 2) end as unitTaxIncome,
        case  when t1.businessArea = 0 then 0
        else round(t1.withoutBuildingTax  / t1.businessArea , 2) end as withoutBuildingSquareTax
        from (
        select  app.serial_no,
        app.project_name,
        b.name as projectType,
        c.name as communityName,
        coalesce(sum(apb.business_area), 0) as businessArea,
        coalesce(sum(apb.building_area), 0) as buildingArea,
        coalesce(apre.settledArea, 0) as settledArea,
        coalesce(apr.totalRoomArea, 0) totalRoomArea,
        count(distinct apb.id) as buildingCount,
        coalesce(apr.totalRoomCount, 0) as totalRoomCount,

        coalesce(e.emptyRoomCount, 0) as emptyRoomCount,
        coalesce(apre.unitCount, 0) as unitCount,
        coalesce(f.entcount, 0) as enCount,
        coalesce(f.registerunitcount, 0) registerUnitCount,
        coalesce(f.registerentcount , 0) registerentCount,

        round(coalesce(d.taxIncome, 0) ,2) as taxIncome,
        round(coalesce(d.withoutBuildingTax, 0),2) as withoutBuildingTax
        from ads_pm_project app
        left join (select * from dm_pm where  type = 'ProjectType') b on b.code = app.project_type
        left join (select * from dm_pm where  type = 'Community') c on c.code = app.community_code
        left join ads_pm_building apb on apb.project_id = app.id
        left join (
        select apre.project_id,
        coalesce(sum(case when moved = false then apre.area else 0 end), 0) settledArea,
        count(distinct apre.enterprise_id) as unitCount
        from ads_pm_room_enterprise apre
        group by apre.project_id
        ) apre  on apre.project_id = app.id
        left join (
        select apr.project_id ,coalesce(sum(apr.room_building_area), 0) totalRoomArea,
        count(distinct apr.room_id) as totalRoomCount
        from view_project_building_floor_room apr
        group by apr.project_id
        )   apr on app.id = apr.project_id
        left join (
        select a.project_id,count(1) as emptyRoomCount from view_project_building_floor_room a
        left join  (select * from ads_pm_room_enterprise a where a.moved = false ) b on a.room_id = b.room_id
        where b.room_id is null
        group by a.project_id
        ) e on app.id = e.project_id
        left join (
        select  c.project_id,sum(by_amt) as taxIncome,
        sum(case when b.financial_split != 1 then by_amt else 0 end )  as  withoutBuildingTax
        from ads_pm_enterprise_tax_fkm a
        inner join ads_pm_enterprise b on a.enterprise_id = b.id
        inner join ads_pm_building c on a.building_id = c.id
        where yskm_dm = 'ss'
        and datekey between #{req.startDate} and #{req.endDate}
        group by c.project_id
        ) d on app.id = d.project_id
        left join (
        select a.project_id,
        coalesce(count(distinct case when b.unit_property in (1,3)  then a.enterprise_id else null end), 0) entCount,
        coalesce(count(distinct case when territorialized = true  then a.enterprise_id else null end), 0) registerUnitCount,
        coalesce(count(distinct case when b.unit_property in (1,3) and territorialized = true then a.enterprise_id else null end), 0) registerEntCount
        from ads_pm_room_enterprise a left join ads_pm_enterprise b on a.enterprise_id = b.id
        where 1=1
       AND a.moved = false
        group by a.project_id
        ) f on app.id = f.project_id
                   where 1=1
        <if test="req.projectName != null and req.projectName != ''">
            and app.project_name like concat('%',#{req.projectName},'%')
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        <if test="req.communityCode != null and req.communityCode != ''">
            and app.community_code = #{req.communityCode}
        </if>
        <choose>
            <when test="req.buildingGroup != null and req.buildingGroup == 'NT'">
                and app.id in (select zs_dm from dm_gy_page_style where type_dm = '14')
            </when>
            <when test="req.buildingGroup != null and req.buildingGroup == 'TJ'">
                and app.id in (select zs_dm from dm_gy_page_style where type_dm = '15')
            </when>

        </choose>
        group by app.serial_no,app.project_name,b.name ,c.name,settledArea,totalRoomArea,totalRoomCount,emptyRoomCount,unitCount,entCount,registerUnitCount,registerentCount,taxIncome,withoutBuildingTax
        ) t1
        where  1=1

        order by t1.serial_no
    </select>
    <select id="listCommunityBuilding" resultMap="communityMenu">
        select code as key ,name as title ,code as value
        from dm_pm
        where type = 'Community'
        order by code
    </select>

    <select id="listCommunityBuildingByCockpit" resultMap="communityMenuByCockpit">
        select code as key ,name as title ,code as value,#{req.projectType} as projectType,'community' as levelType
        from dm_pm
        where type = 'Community'
        order by code
    </select>


    <select id="listProjectMenu" resultMap="projectMenu">
        select id as key ,project_name as title ,id as value
        from ads_pm_project
        where community_code = #{key}

    </select>

    <select id="listProjectMenuWithoutFloor" resultMap="projectMenuWithoutFloor">
        select id as key ,project_name as title ,id as value
        from ads_pm_project
        where community_code = #{key}
    </select>

    <select id="listProjectMenuByCockpit" resultMap="projectMenuByType">
        select id as key ,project_name as title ,id as value, (select name from dm_pm where code = project_type AND type = 'ProjectType') as type, 'project' as levelType
        from ads_pm_project
        where community_code = #{key}
        <if test="projectType != null and projectType != ''">
            and project_type = #{projectType}
        </if>
        order by serial_no
    </select>

    <select id="listBuildingMenu" resultMap="buildingMenu">
        select id as key ,building_name as title ,id as value
        from ads_pm_building
        where project_id = #{key}
    </select>

    <select id="listBuildingMenuWithoutFloor" resultMap="buildingMenuWithoutFloor">
        select id as key ,building_name as title ,id as value
        from ads_pm_building
        where project_id = #{key}
    </select>

    <select id="listBuildingMenuByCockpit" resultMap="buildingMenuByCockpit">
        select id as key ,building_name as title ,id as value,'building' as levelType
        from ads_pm_building
        where project_id = #{key}
          and building_status_code = '3'
        order by serial_no
    </select>

    <select id="listFloorMenu" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key ,floor_name as title ,id as value
        from ads_pm_floor
        where building_id = #{key}
    </select>
    <select id="exportBuildingCondition" resultType="java.util.Map">
        select t1.serial_no as serialNo,t1.*,
        t1.businessArea - t1.settledArea as emptyArea,
        case when t1.businessArea = 0 then 0 else round((t1.businessArea - t1.settledArea) / t1.businessArea * 100, 2)
        end as emptyRate,
        case when t1.businessArea = 0 then 0 else round(t1.settledArea / t1.businessArea * 100, 2) end as settledRate,
        case when t1.unitCount = 0 then 0 else round(t1.registerUnitCount / t1.unitCount * 100, 2) end as registerRate,
        case when t1.businessArea = 0 then 0 else round(t1.taxIncome * 10000 / t1.businessArea , 2) end as
        unitTaxIncome,
        case when t1.businessArea = 0 then 0 else round(t1.withoutBuildingTax * 10000 / t1.businessArea , 2) end as
        withoutBuildingSquareTax
        from (select
        app.serial_no,
        app.project_name,
        (select name from dm_pm where code = app.project_type and type = 'ProjectType') as projectType,
        (select name
        from dm_pm
        where code = app.community_code
        and type = 'Community') as communityName,
        (select coalesce(sum(business_area), 0) from ads_pm_building apb where apb.project_id = app.id)
        as businessArea,
        (select coalesce(sum(area), 0)
        from ads_pm_room_enterprise apre
        where apre.project_id = app.id
        and moved = false) as settledArea,
        (select coalesce(sum(business_area),0)
        from ads_pm_room apr
        where floor_id in (select id
        from ads_pm_floor apf
        where building_id in (select id from ads_pm_building where project_id = app.id))) totalRoomArea,
        (select count(1) from ads_pm_building where project_id = app.id) as buildingCount,
        (select count(1)
        from ads_pm_room apr
        where floor_id in (select id
        from ads_pm_floor apf
        where building_id in (select id from ads_pm_building where project_id = app.id))) as totalRoomCount,
        (select count(1)
        from ads_pm_room apr
        where id not in (select room_id from ads_pm_room_enterprise where project_id = app.id and moved = false)
        and floor_id in (select id
        from ads_pm_floor apf
        where building_id in (select id from ads_pm_building where project_id = app.id))) as emptyRoomCount,
        (select count(1)
        from (select enterprise_id
        from ads_pm_room_enterprise
        where project_id = app.id
        group by enterprise_id) t1) as unitCount,
        (select count(1)
        from ads_pm_enterprise
        where id in
        (select enterprise_id from ads_pm_room_enterprise where project_id = app.id group by enterprise_id)
        and (unit_property = 1 or unit_property = 3)) as entCount,
        (select count(1)
        from ads_pm_enterprise
        where id in
        (select enterprise_id from ads_pm_room_enterprise where project_id = app.id group by enterprise_id)
        and territorialized = true) as registerUnitCount,
        (select count(1)
        from ads_pm_enterprise
        where id in
        (select enterprise_id from ads_pm_room_enterprise where project_id = app.id group by enterprise_id)
        and (unit_property = 1 or unit_property = 3)
        and territorialized = true) as registerEntCount,

        (select coalesce( sum(by_amt) , 0)
        from ads_pm_enterprise_tax_fkm
        where enterprise_id in
        (select enterprise_id
        from ads_pm_room_enterprise
        where project_id = app.id
        group by enterprise_id)
and yskm_dm = 'ss'
        and datekey between #{req.startDate} and #{req.endDate}
        ) as taxIncome,
        (select coalesce(sum(by_amt) ,0)
        from ads_pm_enterprise_tax_fkm
        where enterprise_id in
        (select id
        from ads_pm_enterprise
        where id in
        (select enterprise_id
        from ads_pm_room_enterprise
        where project_id = app.id
        group by enterprise_id)
        and financial_split != 1)
        and yskm_dm = 'ss'
        and datekey between #{req.startDate} and #{req.endDate}
        ) as withoutBuildingTax
        from ads_pm_project app
        where 1=1
        <if test="req.projectName != null and req.projectName != ''">
            and app.project_name like concat('%',#{req.projectName},'%')
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        <if test="req.communityCode != null and req.communityCode != ''">
            and app.community_code = #{req.communityCode}
        </if>
        ) t1
        order by t1.serial_no
    </select>

    <select id="listCommunityBuildingWithoutFloor" resultMap="communityMenuWithoutFloor">
        select code as key ,name as title ,code as value
        from dm_pm
        where type = 'Community'
    </select>
    <select id="getAreaAnalyze" resultType="com.zjhh.economy.vo.analyzecockpit.AreaAnalyzeVo">
        select *
        from
    </select>
    <select id="listProject" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key, project_name as title ,id as value from ads_pm_project
        where 1=1
        <if test="req.projectType != null and req.projectType != ''">
            and project_type = #{req.projectType}
        </if>
        order by serial_no
    </select>
    <select id="getBuildingConditionHj" resultType="com.zjhh.economy.vo.BuildingConditionVo">
        select '合计' as serialNo,
        '合计' as project_name,
        null as projectType,
        '合计' as communityName,
        sum(t1.businessArea) as businessArea,
        sum(t1.buildingArea) as buildingArea,
        sum(t1.settledArea) as settledArea,
        sum(t1.totalRoomArea) as totalRoomArea,
        sum(t1.buildingCount) as buildingCount,
        sum(t1.totalRoomCount) as totalRoomCount,
        sum(t1.emptyRoomCount) as emptyRoomCount,
        sum(t1.unitCount) as unitCount,
        sum(t1.entcount) as entcount,
        sum(t1.registerunitcount) as registerunitcount,
        sum(t1.registerentcount) as registerentcount,
        round(coalesce(sum(t1.taxIncome),0) / 10000 ,2) as taxIncome,
        round(coalesce(sum(t1.withoutBuildingTax),0) / 10000,2) as withoutBuildingTax,
        sum(t1.businessArea - t1.settledArea) as emptyArea,
        case when sum(t1.buildingArea) = 0 then 0
        else round(sum(t1.businessArea - t1.settledArea) / sum(t1.businessArea) * 100, 2) end as emptyRate,
        case when sum(t1.businessArea) = 0 then 0
        else round(sum(t1.settledArea) / sum(t1.businessArea) * 100, 2) end as settledRate,
        case when sum(t1.unitCount) = 0 then 0
        else round(sum(t1.registerUnitCount)::decimal / sum(t1.unitCount)::decimal * 100, 2) end as registerRate,
        case when sum(t1.businessArea) = 0 then 0
        else round(sum(t1.taxIncome)  / sum(t1.businessArea) , 2) end as unitTaxIncome,
        case when sum(t1.businessArea) = 0 then 0
        else round(sum(t1.withoutBuildingTax)  / sum(t1.businessArea) , 2) end as withoutBuildingSquareTax
        from (
        select  app.serial_no,
        app.project_name,
        b.name as projectType,
        c.name as communityName,
        coalesce(sum(apb.business_area), 0) as businessArea,
        coalesce(sum(apb.building_area), 0) as buildingArea,
        coalesce(apre.settledArea, 0) as settledArea,
        coalesce(apr.totalRoomArea, 0) totalRoomArea,
        count(distinct apb.id) as buildingCount,
        coalesce(apr.totalRoomCount, 0) as totalRoomCount,
        coalesce(e.emptyRoomCount, 0) as emptyRoomCount,
        coalesce(apre.unitCount, 0) as unitCount,
        coalesce(f.entcount, 0) entcount,
        coalesce(f.registerunitcount, 0) registerunitcount,
        coalesce(f.registerentcount , 0) registerentcount,
        coalesce(d.taxIncome, 0) taxIncome,
        coalesce(d.withoutBuildingTax, 0) withoutBuildingTax
        from ads_pm_project app
        left join (select * from dm_pm where  type = 'ProjectType') b on b.code = app.project_type
        left join (select * from dm_pm where  type = 'Community') c on c.code = app.community_code
        left join ads_pm_building apb on apb.project_id = app.id
        left join (
        select apre.project_id,
        coalesce(sum(case when moved = false then apre.area else 0 end), 0) settledArea,
        count(distinct apre.enterprise_id) as unitCount
        from ads_pm_room_enterprise apre
        group by apre.project_id
        ) apre  on apre.project_id = app.id
        left join (
        select apr.project_id ,coalesce(sum(apr.room_building_area), 0) totalRoomArea,
        count(distinct apr.room_id) as totalRoomCount
        from view_project_building_floor_room apr
        group by apr.project_id
        )   apr on app.id = apr.project_id
        left join (
        select a.project_id,count(1) as emptyRoomCount from view_project_building_floor_room a
        left join  (select * from ads_pm_room_enterprise a where a.moved = false ) b on a.room_id = b.room_id
        where b.room_id is null
        group by a.project_id
        ) e on app.id = e.project_id
        left join (
        select  c.project_id,sum(by_amt) as taxIncome,
        sum(case when b.financial_split != 1 then by_amt else 0 end ) withoutBuildingTax
        from ads_pm_enterprise_tax_fkm a
        inner join ads_pm_enterprise b on a.enterprise_id = b.id
        inner join ads_pm_building c on a.building_id = c.id
        where yskm_dm = 'ss'
        and datekey between #{req.startDate} and #{req.endDate}
        group by c.project_id
        ) d on app.id = d.project_id
        left join (
        select a.project_id,
        coalesce(count(distinct case when b.unit_property in (1,3)  then a.enterprise_id else null end), 0) entCount,
        coalesce(count(distinct case when territorialized = true  then a.enterprise_id else null end), 0) registerUnitCount,
        coalesce(count(distinct case when b.unit_property in (1,3) and territorialized = true then a.enterprise_id else null end), 0) registerEntCount
        from ads_pm_room_enterprise a left join ads_pm_enterprise b on a.enterprise_id = b.id
        where 1=1
        AND a.moved = false
        group by a.project_id
        ) f on app.id = f.project_id
        where 1=1
        <if test="req.projectName != null and req.projectName != ''">
            and app.project_name like concat('%',#{req.projectName},'%')
        </if>
        <if test="req.projectType != null and req.projectType != ''">
            and app.project_type = #{req.projectType}
        </if>
        <if test="req.communityCode != null and req.communityCode != ''">
            and app.community_code = #{req.communityCode}
        </if>
        <choose>
            <when test="req.buildingGroup != null and req.buildingGroup == 'NT'">
                and app.id in (select zs_dm from dm_gy_page_style where type_dm = '14')
            </when>
        <when test="req.buildingGroup != null and req.buildingGroup == 'TJ'">
            and app.id in (select zs_dm from dm_gy_page_style where type_dm = '15')
        </when>

        </choose>
        group by app.serial_no,app.project_name,b.name ,c.name,settledArea,totalRoomArea,totalRoomCount,emptyRoomCount,unitCount,entcount,registerunitcount,registerentcount,taxIncome,withoutBuildingTax
        ) t1
        where  1=1
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsDmsReportDataSetMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, report_type_code, data_type_code, data_set_name, version, create_time, update_time
    </sql>

    <resultMap id="AddAlysReportDataSetReq" type="com.zjhh.economy.request.earlywarning.AddAlysReportDataSetReq">
        <result column="report_type_code" property="reportTypeCode"/>
        <result column="data_type_code" property="dataTypeCode"/>
        <result column="data_set_name" property="dataSetName"/>
        <result column="key" property="key"/>
        <result column="parent_key" property="parentKey"/>
        <result column="title" property="title"/>
        <result column="data_set_id" property="dataSetId"/>
        <collection property="children" ofType="com.zjhh.economy.request.earlywarning.AddAlysReportDataColumnSetReq">
            <result column="column_key" property="columnKey"/>
            <result column="column_name" property="columnName"/>
            <result column="field" property="field"/>
            <result column="col_key" property="key"/>
            <result column="col_parent_key" property="parentKey"/>
            <result column="col_title" property="title"/>
        </collection>
    </resultMap>

    <select id="getDataSet" resultMap="AddAlysReportDataSetReq">
        select t1.report_type_code,
               t1.data_type_code,
               t1.data_set_name,
               t1.id as key,
               t1.data_set_name                          as title,
               '0'                                       as parent_key,
               t1.id                                     as data_set_id,
               t2.column_key,
               t2.column_name,
               t2.id                                     as col_key,
               t2.column_name                            as col_title,
               t2.data_set_id                            as col_parent_key,
               case
               when t2.column_key is null then null
               else
                   concat(concat(t1.id, '.'), t2.column_key)
        end as field
        from ads_dms_report_data_set t1
                 left join ads_dms_report_data_column_set t2 on t1.id = t2.data_set_id
        where report_type_code = #{reportTypeCode}
        order by t1.id, substr(t2.column_key, 5)::decimal
    </select>

    <select id="findDataValue" resultType="java.util.Map">
        select t1.*
        from ads_dms_report_data t1
                 left join ads_dms_report_data_set t2
                           on t1.report_type_code = t2.report_type_code and t1.data_type_code = t2.data_type_code
        where t2.id = #{dataSetId}
          and t1.datekey = #{datekey} limit 1
    </select>

    <select id="listDataValue" resultType="java.util.Map">
        select
        <foreach collection="selectDatas" item="selectData" separator=",">
            ${selectData}
        </foreach>
        from ads_dms_report_data t1
        left join ads_dms_report_data_set t2
        on t1.report_type_code = t2.report_type_code and t1.data_type_code = t2.data_type_code
        where t2.id = #{dataSetId}
        and t1.datekey = #{datekey}
    </select>
</mapper>

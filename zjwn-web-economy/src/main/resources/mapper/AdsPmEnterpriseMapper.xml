<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjhh.economy.dao.mapper.AdsPmEnterpriseMapper">



    <sql id="checkProject">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
        inner join v_p_building t2 on apre.building_id = t2.id
        where apre.project_id = #{req.projectId}
        and apre.moved = false
    </sql>
    <sql id="checkCommunity">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
                 inner join v_p_building t2 on apre.building_id = t2.id
        where apre.project_id in (select id from ads_pm_project where community_code = #{req.communityCode})
          and apre.moved = false
    </sql>

    <sql id="checkBuilding">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
                 inner join v_p_building t2 on apre.building_id = t2.id
        where apre.building_id = #{req.buildingId}
          and apre.moved = false
    </sql>

    <sql id="checkRoom">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
                 inner join v_p_building t2 on apre.building_id = t2.id
        where apre.room_id in (select id from ads_pm_room where room_no like concat('%', #{req.roomNo}, '%'))
          and apre.moved = false
    </sql>

    <sql id="checkFocus">
        select distinct enterprise_id
        from ads_pm_enterprise_focus apef
        where user_code = #{req.userCode}
    </sql>


    <sql id="checkSettleIN">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
        where moved = false
    </sql>
    <sql id="checkSettleMove">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
        where moved = true
        and enterprise_id not in (select distinct enterprise_id
                                  from ads_pm_room_enterprise apre
                                  where moved = false)
    </sql>
    <sql id="checkNotIN">
        select distinct enterprise_id
        from ads_pm_room_enterprise apre
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , enterprise_name, uscc, old_name, found_date, legal_person, enterprise_type_code, industry_code, registered_capital, residence, phone, on_scaled, territorialized, business_term_start, business_term_end, financing_stage_code, registered_address, business_scope, logo_img_id, create_user, create_time, update_time
    </sql>
    <select id="getMaxEntSerialNo" resultType="java.lang.String">
        select max(serial_no)
        from ads_pm_enterprise
        where serial_no like concat(#{date}, '%')
    </select>
    <select id="getEnterpriseDetail" resultType="com.zjhh.economy.vo.EnterpriseDetailVo">

        select id,
               enterprise_name,
               uscc,
               serial_no,
               old_name,
               found_date,
               legal_person,
               (select name
                from dm_pm
                where type = 'EnterpriseType' and code = enterprise_type_code) as enterpriseTypeName,
               enterprise_type_code,
               industry_code,
               (select name from dm_gy_hy where code = industry_code)        as industryName,
               registered_capital,
               currency_type,
               residence,
               phone,
               on_scaled,
               territorialized,
               business_term_start,
               business_term_end,
               (select name
                from dm_pm
                where type = 'FinancingStage'
                  and code = financing_stage_code)                             as financingStageName,
               financing_stage_code,
               registered_address,
               business_scope,
               logo_img_id,
               ent_contact_person,
               ent_phone
        From ads_pm_enterprise ape 
        where id = #{enterpriseId}
    </select>
    <select id="pageEnterpriseList" resultType="com.zjhh.economy.vo.EnterpriseListVo">

        -- 首先创建递归CTE获取完整的行业层级路径
        WITH RECURSIVE industry_hierarchy AS (
        -- 基础查询：获取第三级作为起始节点
        SELECT
        code,
        name,
        parent_code,
        name::character varying(500) as industry_path,
        3 as level  -- 从第三级开始
        FROM dm_gy_hy
        WHERE parent_code IN (
        SELECT code FROM dm_gy_hy
        WHERE parent_code IN (
        SELECT code FROM dm_gy_hy
        WHERE parent_code IS NULL OR parent_code = '' OR parent_code = code
        OR parent_code NOT IN (SELECT code FROM dm_gy_hy WHERE code IS NOT NULL)
        )
        )

        UNION ALL

        -- 递归查询：构建从第三级开始的完整路径
        SELECT
        child.code,
        child.name,
        child.parent_code,
        (parent.industry_path || '/' || child.name)::character varying(500) as industry_path,
        parent.level + 1 as level
        FROM dm_gy_hy child
        INNER JOIN industry_hierarchy parent ON child.parent_code = parent.code
        WHERE child.parent_code != child.code AND parent.level &lt; 10  -- 防止无限递归，最多10层
        )
        select t1.*,ih.industry_path as industryName From (
        select ape.*,
        t1.*,
        case when (select count(1) from ads_pm_enterprise_focus where enterprise_id = ape.id and user_code = #{req.userCode}) > 0 then true else false end as focused,
        case when (select count(1) from ads_pm_room_enterprise where enterprise_id = ape.id and moved = false) > 0 then 1
        when (select count(1) from ads_pm_room_enterprise where enterprise_id = ape.id and moved = true) > 0 and (select count(1) from ads_pm_room_enterprise where enterprise_id = ape.id and moved = false) = 0 then 2
        else 3
        end as settleStatus,
        (select name from dm_pm where code = ape.enterprise_type_code and type = 'EnterpriseType') as enterpriseTypeName,
        (select string_agg(dp.name,'、') from ads_pm_enterprise_label apel left join dm_pm dp on apel.label_code = dp.code and dp.type = 'EnterpriseLabel' where apel.enterprise_id = ape.id) as enterpriseLabels,
        coalesce(buildingInfo,'OTHER_BUILDING') as buildingId
        from ads_pm_enterprise ape
        left join (
        select
        t2.enterprise_id,
        string_agg(case when t2.move_status = 'checkin' then t2.settleInfo end, ';') as settleInfo,  -- 入驻信息
        string_agg(case when t2.move_status = 'moveout' then t2.settleInfo end, ';') as moveOutInfo, -- 搬离信息
        string_agg(case when t2.move_status = 'checkin' then t2.buildingInfo end, ';') as buildingInfo,
        min(case when t2.move_status = 'checkin' then t2.registerDate end) as registerDate,  -- 入驻日期
        min(case when t2.move_status = 'moveout' then t2.registerDate end) as moveOutDate    -- 搬离日期
        from (
        select
        communityName || '-' || project_name || '-' || building_name || string_agg(concat(floor_name, '-', room_no), '、' order by room_no) as settleInfo,
        enterprise_id,
        string_agg(building_id,',') as buildingInfo,
        min(registerDate) as registerDate,
        move_status
        from (
        select
        (select name from dm_pm dp where dp.code = app.community_code and type = 'Community') as communityName,
        app.project_name,
        apb.id as building_id,
        apb.building_name,
        apf.floor_name,
        apr.room_no,
        ape.enterprise_id,
        min(ape.check_in_date) as registerDate,
        case when ape.moved = false then 'checkin' else 'moveout' end as move_status
        from ads_pm_room_enterprise ape
        left join ads_pm_project app on app.id = ape.project_id
        inner join v_p_building apb on ape.building_id = apb.id
        left join ads_pm_floor apf on ape.floor_id = apf.id
        left join ads_pm_room apr on ape.room_id = apr.id
        group by communityName, app.project_name, apb.building_name, apf.floor_name, apr.room_no, ape.enterprise_id, apb.id, ape.moved
        ) t1
        group by building_name, project_name, communityName, enterprise_id, move_status
        ) t2
        group by enterprise_id
        ) t1 on t1.enterprise_id = ape.id
        <where>
            <if test="req.labels != null and req.labels.size > 0">
               ape.id in (select enterprise_id from ads_pm_enterprise_label where label_code in
                <foreach collection="req.labels" item="label" open="(" separator="," close=")">
                    #{label}
                </foreach>)
            </if>
            <if test="req.enterpriseName != '' and req.enterpriseName != ''">
                and ape.enterprise_name like concat('%',#{req.enterpriseName},'%')
            </if>
            <if test="req.onScaled != null">
                and ape.on_scaled = #{req.onScaled}
            </if>
            <if test="req.territorialized != null">
                and ape.territorialized = #{req.territorialized}
            </if>
            <if test="req.industryCode != null and req.industryCode.size > 0">
                AND industry_code in (select hy_dm from dm_ly_hy_zq a where a.sjhy_dm in
                <foreach collection="req.industryCode" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
                )
            </if>
            <if test="req.projectId != null and req.projectId != ''">
                and ape.id in (<include refid="checkProject"></include>)
            </if>
            <if test="req.communityCode != null and req.communityCode != ''">
                and ape.id in (<include refid="checkCommunity"></include>)
            </if>
            <if test="req.buildingId != null and req.buildingId != ''">
                and ape.id in (<include refid="checkBuilding"></include>)
            </if>
            <if test="req.projectType != null and req.projectType != ''">
                and ape.id in (select enterprise_id from ads_pm_room_enterprise t1 left join ads_pm_project t2
                on t1.project_id = t2.id
                where t2.project_type = #{req.projectType})
            </if>
            <if test="req.roomNo != null and req.roomNo != ''">
                and ape.id in (<include refid="checkRoom"></include>)
            </if>
            <choose>
                <when test="req.focused != null and req.focused == true">
                    and ape.id in (<include refid="checkFocus"></include>)
                </when>
                <when test="req.focused != null and req.focused == false">
                    and ape.id not in (<include refid="checkFocus"></include>)
                </when>
            </choose>
            <choose>
                <when test="req.settleStatus != null and req.settleStatus == 1">
                    and ape.id in (<include refid="checkSettleIN"></include>)
                </when>
                <when test="req.settleStatus != null and req.settleStatus == 2">
                    and ape.id in (<include refid="checkSettleMove"></include>) and ape.id not in (<include refid="checkSettleIN"></include>)
                </when>
                <when test="req.settleStatus != null and req.settleStatus == 3">
                    and ape.id not in (<include refid="checkNotIN"></include>)
                </when>
            </choose>
        </where>
        ) t1
        left join industry_hierarchy ih on ih.code = t1.industry_code
        left join (
        select enterprise_id, coalesce(sum(bq_amt) / 10000,0) as taxIncome
        from ads_pm_enterprise_tax_fkm
        where yskm_dm = 'ss' and datekey = #{req.datekey}
        group by enterprise_id
        ) t2 on t1.enterprise_id = t2.enterprise_id
        inner join v_p_building vpb on position(vpb.id in t1.buildingId) = 1
        where 1=1
        <choose>
            <when test="req.greaterThan != null and req.greaterThan == 1">
                and coalesce(t2.taxIncome,0) >= 50
            </when>
            <when test="req.greaterThan != null and req.greaterThan == 2">
                and coalesce(t2.taxIncome,0) &lt; 50
            </when>
        </choose>
        order by coalesce(t2.taxIncome,0) desc, t1.enterprise_id asc
    </select>
    <select id="checkEntExists" resultType="java.lang.Boolean">
        select case when count(1) > 0 then true else false end
        from ads_pm_enterprise
        where (
            enterprise_name = #{enterpriseName}
            <if test="oldName != null and oldName != ''">
                or old_name = #{oldName}
            </if>
        )
         <if test="id != null and id != ''">
             and id != #{id}
         </if>
    </select>
    <select id="listSearchEnt" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key, enterprise_name as title, id as value from ads_pm_enterprise
        where
           1=1
        <if test="req.entName != null and req.entName != ''">
            and  enterprise_name like concat('%',#{req.entName},'%')
        </if>
        order by id
        limit 20
    </select>
    <select id="getBriefEntUpdateCount" resultType="java.lang.Integer">
        select coalesce(count(1),0) from ads_pm_enterprise
        where
            create_time::date not between #{startDate}::date and #{endDate}::date
        and update_time::date between #{startDate}::date and #{endDate}::date
    </select>
    <select id="listMenuEnt" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key, enterprise_name as title, id as value
            from ads_pm_enterprise ape
            where 1=1
            <choose>
                <when test="req.keyword != null and req.keyword != ''">
                    and enterprise_name like concat('%',#{req.keyword},'%')
                </when>
            <otherwise>
                and enterprise_name in ('嘉兴市燃气集团股份有限公司','嘉兴万达广场投资有限公司','嘉兴市天然气管网经营有限公司','嘉兴华昭贸易有限公司','嘉兴国际商务区投资建设集团有限公司','嘉兴市嘉源环境卫生管理有限责任公司','浙江诚理企业管理有限公司','中国移动通信集团浙江有限公司嘉兴分公司','浙江嘉化能源供应链有限公司','浙江嘉宇工程管理有限公司','嘉兴睿源能源科技有限公司','嘉兴市嘉城环艺物业经营管理有限公司','嘉兴老年医疗中心有限公司')
            </otherwise>
            </choose>

        order by ape.enterprise_name
        limit 20
    </select>
    <select id="listMenuMobileEnt" resultType="com.zjhh.comm.vo.TreeSelectVo">
        select id as key, enterprise_name as title, id as value from ads_pm_enterprise
        where
        1=1
        <if test="req.entName != null and req.entName != ''">
            and  enterprise_name like concat('%',#{req.entName},'%')
        </if>
        order by id
        limit 10
    </select>

</mapper>

server:
    port: 7009
spring:
    datasource:
        dynamic:
            datasource:
                master:
                    url: *****************************************************************
                    username: <PERSON><PERSON>(qE4ELkMJQkrVgcwRZ4p/ItA26FQTJjTF)
                    password: <PERSON><PERSON>(p6Ec62HR/YbLHCwkKwyHsYdxqQOpJiob)
                    driver-class-name: com.p6spy.engine.spy.P6SpyDriver

    data:
        redis:
            host: *************
            port: 6379
            password: <PERSON><PERSON>(tkAQdDuR0B492GIkUW3GFSxooCIGwtU5)
            database: 0

sa-token:
    # token有效期，单位s 默认30天, -1代表永不过期
    timeout: 86400
    # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
    active-timeout: 3600

jetcache:
    remote:
        default:
            uri:
                - redis://${spring.data.redis.password}@${spring.data.redis.host}:${spring.data.redis.port}/${spring.data.redis.database}
            poolConfig:
                minIdle: 5
                maxIdle: 20
                maxTotal: 50

system:
    zwdd-auth:
        appKey: justtest-1wV3d5a5eUShi39zIBn22
        appSecret: 0N0m5j5BoIb520bz3Aqzp6550iNkHXqcTCa7o6Hw
        scanAppKey: wnx_dingoa-jrc28C0FkGX0WjqiIRj
        scanAppSecret: pb0AQtj8SmIPShp2SL6tYYCH7lGhMj8c5zxTU687
        protocal: https
        tenantId: 50083946
        domainName: openplatform.dg-work.cn

chat-bi:
    base-url: http://*************:9080
    login-name: test
    login-password: e6+jQ26AESREiBBuKM1u1A==

onlyoffice:
    only-office-url: http://*************:8109/onlyoffice
    only-office-security-key: cnQNxk435jEsgeLUv22BTwi7xCxgbObH
    server-url: http://************:7188
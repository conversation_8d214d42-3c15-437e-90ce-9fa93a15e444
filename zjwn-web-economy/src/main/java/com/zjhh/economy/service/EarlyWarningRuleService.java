package com.zjhh.economy.service;


import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDmsReport;
import com.zjhh.economy.dao.entity.AdsDmsReportLog;
import com.zjhh.economy.dao.entity.AdsDmsReportPage;
import com.zjhh.economy.request.earlywarning.*;
import com.zjhh.economy.vo.earlywarning.AdsDmsReportVo;

import java.util.List;
import java.util.Map;

/**
 * 预警规则
 *
 * <AUTHOR>
 * @since 2021/11/19 09:39
 */
public interface EarlyWarningRuleService {

    /**
     * 分析报告模板列表
     *
     * @param req
     * @return
     */
    Page<AdsDmsReportVo> pageAnalysisReport(PageAnalysisReportReq req);

    /**
     * 新增分析报告模板
     *
     * @param report
     */
    void addAlysReport(AddAdsDmsReportReq req);

    /**
     * 判断分析报告模板编码
     *
     * @param reportTypeCode
     * @return
     */
    Boolean checkAlysReportCode(String reportTypeCode);

    /**
     * 批量删除分析报告模板
     *
     * @param reportTypeCodes
     */
    void delAlysReport(List<String> reportTypeCodes);

    /**
     * 编辑分析报告模板
     *
     * @param report
     */
    void editAlysReport(AddAdsDmsReportReq req);

    /**
     * 分析报告日志列表
     *
     * @param req
     * @return
     */
    Page<AdsDmsReportLog> pageAlysReportLog(PageAlysReportLogReq req);

    /**
     * 获取分析报告日志详情
     *
     * @param id
     * @return
     */
    AdsDmsReportLog getAlysReportLogInfo(String id);

    /**
     * 获取分析报告数据集
     *
     * @param reportTypeCode
     * @return
     */
    List<AddAlysReportDataSetReq> getDataSet(String reportTypeCode);

    /**
     * 添加数据集
     *
     * @param req
     */
    void addDataSet(AddAlysReportDataSetReq req);

    /**
     * 编辑数据源
     *
     * @param req
     */
    void editDataSet(UpdateAlysReportDataSetReq req);

    /**
     * 删除数据源
     *
     * @param dataSetId
     */
    void delDataSet(String dataSetId);

    /**
     * 获取分析报告界面
     *
     * @param reportTypeCode
     * @return
     */
    AdsDmsReport getAlysReportPage(String reportTypeCode);

    /**
     * 保存分析报告界面
     *
     * @param page
     */
    void saveAlysReportPage(SaveAlysReportPageReq req);

    /**
     * 保存和预览
     *
     * @param page
     */
    String saveAndPreviewAlysReport(SaveAndPreviewAlysReportReq req);

    /**
     * 判断报告是否存在
     *
     * @param req
     * @return
     */
    Boolean checkReportDocExist(CreateReportDocReq req);

    /**
     * 预警分析报告生成
     *
     * @param req
     */
    void createReportDoc(CreateReportDocReq req);

    /**
     * 预览预警分析报告
     *
     * @param reportTypeCode
     */
    String previewReportDoc(PreviewReportDocReq req);

    /**
     * 初始化预警分析报告定时任务
     */
    void initTask();

    /**
     * 获取可选择的行政区划
     *
     * @return
     */
    List<TreeSelectVo> listSelectOrgCodes();
}

package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.onlyoffice.context.DocsIntegrationSdkContext;
import com.onlyoffice.manager.security.JwtManager;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.utils.SnowflakeUtil;
import com.zjhh.comm.utils.TreeUtils;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.onlyoffice.constant.OnlyOfficeConstant;
import com.zjhh.economy.onlyoffice.report.TemplateFillDataBuilder;
import com.zjhh.economy.onlyoffice.report.TemplateParameterAnalyzer;
import com.zjhh.economy.onlyoffice.report.dto.ParameterAnalysisResult;
import com.zjhh.economy.onlyoffice.template.WordTemplateEngine;
import com.zjhh.economy.onlyoffice.template.model.TemplateFillData;
import com.zjhh.economy.onlyoffice.template.model.TemplateParseResult;
import com.zjhh.economy.request.earlywarning.*;
import com.zjhh.economy.service.EarlyWarningRuleService;
import com.zjhh.economy.vo.earlywarning.AdsDmsReportVo;
import com.zjhh.system.dao.entity.QuartzJob;
import com.zjhh.system.service.DictService;
import com.zjhh.system.utils.QuartzUtil;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronExpression;
import org.quartz.Scheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/19 09:39
 */
@Slf4j
@Service
@DS("master")
public class EarlyWarningRuleServiceImpl implements EarlyWarningRuleService {

    @Resource
    private AdsMoaDiReportMapper adsMoaDiReportMapper;

    @Resource
    private AdsDmsReportMapper adsDmsReportMapper;

    @Resource
    private AdsDmsReportLogMapper adsDmsReportLogMapper;

    @Resource
    private AdsDmsReportDataSetMapper adsDmsReportDataSetMapper;

    @Resource
    private AdsDmsReportDataColumnSetMapper adsDmsReportDataColumnSetMapper;

    @Resource
    private AdsDocumentMapper adsDocumentMapper;

    @Resource
    private UserSession userSession;

    private static final String FILE_PATH = "alys_report";

    @Resource
    private DictService dictService;

    @Resource
    private Scheduler scheduler;

    @Resource
    private AdsDmsReportAuthMapper adsDmsReportAuthMapper;

    @Resource
    private DocsIntegrationSdkContext docsIntegrationSdkContext;

    @Resource
    private WordTemplateEngine wordTemplateEngine;

    @Resource
    private TemplateParameterAnalyzer templateParameterAnalyzer;

    @Resource
    private TemplateFillDataBuilder templateFillDataBuilder;

    private final static String TASK_CLASS = "com.zjhh.economy.task.AnalyzeReportTask";

    @Override
    public Page<AdsDmsReportVo> pageAnalysisReport(PageAnalysisReportReq req) {
        if (ObjectUtil.isEmpty(req.getReportState())) {
            req.setReportState(99);
        }
        return adsDmsReportMapper.pageAnalysisReport(req.getPage(AdsDmsReportVo.class), req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addAlysReport(AddAdsDmsReportReq req) {
        if (checkAlysReportCode(req.getReportTypeCode())) {
            throw new BizException("该模板编码已存在！");
        }
        if (!CronExpression.isValidExpression(req.getCronExpression())) {
            throw new BizException("cron表达式错误！");
        }
        AdsDmsReport report = BeanUtil.copyProperties(req, AdsDmsReport.class);
        report.setCreateUser(userSession.getSessionLoginVo().getLoginName());
        report.setCreateTime(LocalDateTime.now());
        report.setDocId(req.getDocId());

        if (req.getAuthType() == 2 && CollUtil.isNotEmpty(req.getOrgCodes())) {
            List<AdsDmsReportAuth> list = new ArrayList<>();
            req.getOrgCodes().forEach(orgCode -> {
                AdsDmsReportAuth auth = new AdsDmsReportAuth();
                auth.setId(IdUtil.getSnowflakeNextIdStr());
                auth.setReportTypeCode(report.getReportTypeCode());
                auth.setOrgCode(orgCode);
                list.add(auth);
            });
            adsDmsReportAuthMapper.insertBatchSomeColumn(list);
        }

        adsDmsReportMapper.insert(report);
        if (report.getCronState() == 1) {
            addQuartzJob(report);
        }
    }

    @Override
    public Boolean checkAlysReportCode(String reportTypeCode) {
        QueryWrapper<AdsDmsReport> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReport::getReportTypeCode, reportTypeCode);
        return adsDmsReportMapper.selectCount(wrapper) > 0;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void delAlysReport(List<String> reportTypeCodes) {
        adsDmsReportMapper.deleteByIds(reportTypeCodes);
        reportTypeCodes.forEach(reportTypeCode -> {
            String key = createAlysJobKey(reportTypeCode);
            if (QuartzUtil.checkScheduleJob(scheduler, key)) {
                QuartzUtil.deleteScheduleJob(scheduler, key);
            }
        });
        QueryWrapper<AdsDmsReportDataSet> adsDmsReportDataSetWrapper = new QueryWrapper<>();
        adsDmsReportDataSetWrapper.lambda().in(AdsDmsReportDataSet::getReportTypeCode, reportTypeCodes)
                .select(AdsDmsReportDataSet::getId);
        List<String> adsDmsReportDataSetIds = adsDmsReportDataSetMapper.selectStrings(adsDmsReportDataSetWrapper);
        if (CollUtil.isNotEmpty(adsDmsReportDataSetIds)) {
            adsDmsReportDataSetMapper.deleteByIds(adsDmsReportDataSetIds);
            QueryWrapper<AdsDmsReportDataColumnSet> adsDmsReportDataColumnSetWrapper = new QueryWrapper<>();
            adsDmsReportDataColumnSetWrapper.lambda().in(AdsDmsReportDataColumnSet::getDataSetId, adsDmsReportDataSetIds);
            adsDmsReportDataColumnSetMapper.delete(adsDmsReportDataColumnSetWrapper);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void editAlysReport(AddAdsDmsReportReq req) {
        if (!CronExpression.isValidExpression(req.getCronExpression())) {
            throw new BizException("cron表达式错误！");
        }
        AdsDmsReport report = adsDmsReportMapper.selectById(req.getReportTypeCode());
        BeanUtil.copyProperties(req, report);
        adsDmsReportMapper.updateById(report);
        String jobKey = createAlysJobKey(report.getReportTypeCode());
        if (report.getCronState() == 1) {
            if (QuartzUtil.checkScheduleJob(scheduler, jobKey)) {
                QuartzJob quartzJob = new QuartzJob();
                quartzJob.setJobKey(jobKey);
                quartzJob.setCronExpression(report.getCronExpression());
                QuartzUtil.updateScheduleJob(scheduler, quartzJob);
            } else {
                addQuartzJob(report);
            }
        } else {
            if (QuartzUtil.checkScheduleJob(scheduler, jobKey)) {
                QuartzUtil.deleteScheduleJob(scheduler, jobKey);
            }
        }
    }

    @Override
    public AdsDmsReportLog getAlysReportLogInfo(String id) {
        QueryWrapper<AdsDmsReportLog> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReportLog::getId, id)
                .select(AdsDmsReportLog::getId, AdsDmsReportLog::getLogInfo);
        return adsDmsReportLogMapper.selectOne(wrapper);
    }

    @Override
    public Page<AdsDmsReportLog> pageAlysReportLog(PageAlysReportLogReq req) {
        QueryWrapper<AdsDmsReportLog> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(AdsDmsReportLog::getId, AdsDmsReportLog::getReportTypeCode,
                AdsDmsReportLog::getReportState, AdsDmsReportLog::getDatekey, AdsDmsReportLog::getReportState,
                AdsDmsReportLog::getRemark, AdsDmsReportLog::getCreateTime);
        if (ObjectUtil.isEmpty(req.getReportState())) {
            req.setReportState(99);
        }
        switch (req.getReportState()) {
            case 0:
            case 1:
                wrapper.lambda().eq(AdsDmsReportLog::getReportState, req.getReportState());
                break;
        }
        wrapper.lambda().eq(AdsDmsReportLog::getReportTypeCode, req.getReportTypeCode());
        if (StrUtil.isNotBlank(req.getStartTime())) {
            LocalDateTime startTime = LocalDateTime.parse(req.getStartTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"));
            wrapper.lambda().ge(AdsDmsReportLog::getCreateTime, startTime);
        }
        if (StrUtil.isNotBlank(req.getEndTime())) {
            LocalDateTime endTime = LocalDateTime.parse(req.getEndTime() + " 23:59:59", DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"));
            wrapper.lambda().le(AdsDmsReportLog::getCreateTime, endTime);
        }
        wrapper.lambda().orderByDesc(AdsDmsReportLog::getCreateTime);
        return adsDmsReportLogMapper.selectPage(req.getPage(AdsDmsReportLog.class), wrapper);
    }

    @Override
    public List<AddAlysReportDataSetReq> getDataSet(String reportTypeCode) {
        return adsDmsReportDataSetMapper.getDataSet(reportTypeCode);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addDataSet(AddAlysReportDataSetReq req) {
        QueryWrapper<AdsDmsReportDataSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReportDataSet::getReportTypeCode, req.getReportTypeCode())
                .eq(AdsDmsReportDataSet::getDataTypeCode, req.getDataTypeCode())
                .eq(AdsDmsReportDataSet::getDataSetName, req.getDataSetName());
        if (adsDmsReportDataSetMapper.selectCount(wrapper) > 0) {
            throw new BizException("当前分析报告下该业务编码已存在！");
        }
        AdsDmsReportDataSet dataSet = new AdsDmsReportDataSet();
        dataSet.setReportTypeCode(req.getReportTypeCode());
        dataSet.setDataTypeCode(req.getDataTypeCode());
        dataSet.setDataSetName(req.getDataSetName());
        dataSet.setCreateTime(LocalDateTime.now());
        dataSet.setVersion(1);
        dataSet.setUpdateTime(LocalDateTime.now());
        adsDmsReportDataSetMapper.insert(dataSet);
        addColumnSet(req.getChildren(), dataSet.getId());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void editDataSet(UpdateAlysReportDataSetReq req) {
        AdsDmsReportDataSet dataSet = new AdsDmsReportDataSet();
        dataSet.setId(req.getDataSetId());
        dataSet.setReportTypeCode(req.getReportTypeCode());
        dataSet.setDataTypeCode(req.getDataTypeCode());
        dataSet.setDataSetName(req.getDataSetName());
        dataSet.setUpdateTime(LocalDateTime.now());
        adsDmsReportDataSetMapper.updateById(dataSet);
        QueryWrapper<AdsDmsReportDataColumnSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReportDataColumnSet::getDataSetId, req.getDataSetId());
        adsDmsReportDataColumnSetMapper.delete(wrapper);
        addColumnSet(req.getChildren(), req.getDataSetId());
    }

    private void addColumnSet(List<AddAlysReportDataColumnSetReq> columns, String dataSetId) {
        if (CollUtil.isEmpty(columns)) {
            return;
        }
        List<AdsDmsReportDataColumnSet> columnSets = new ArrayList<>(columns.size());
        columns.forEach(column -> {
            AdsDmsReportDataColumnSet columnSet = new AdsDmsReportDataColumnSet();
            columnSet.setDataSetId(dataSetId);
            columnSet.setColumnName(column.getColumnName());
            columnSet.setColumnKey(column.getColumnKey());
            columnSet.setCreateTime(LocalDateTime.now());
            columnSet.setUpdateTime(LocalDateTime.now());
            columnSet.setVersion(1);
            columnSets.add(columnSet);
        });
        adsDmsReportDataColumnSetMapper.insertBatchSomeColumn(columnSets);
    }

    @Override
    public void delDataSet(String dataSetId) {
        adsDmsReportDataSetMapper.deleteById(dataSetId);
        QueryWrapper<AdsDmsReportDataColumnSet> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReportDataColumnSet::getDataSetId, dataSetId);
        adsDmsReportDataColumnSetMapper.delete(wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AdsDmsReport getAlysReportPage(String reportTypeCode) {
        AdsDmsReport report = adsDmsReportMapper.selectById(reportTypeCode);
        if (StrUtil.isBlank(report.getDocId())) {
            try {
                // 创建新的文档记录
                AdsDocument adsDocument = new AdsDocument();
                String id = IdUtil.getSnowflakeNextIdStr();
                LocalDateTime now = LocalDateTime.now();
                String filepath = FILE_PATH + File.separator + now.getYear() + File.separator + now.getMonthValue() +
                        File.separator + now.getDayOfMonth() + File.separator + id + ".docx";

                adsDocument.setId(id);
                adsDocument.setTitle(report.getReportTypeName() + ".docx");
                adsDocument.setPath(filepath);
                adsDocument.setCreateUser(userSession.getUserCode());
                adsDocument.setCreateTime(now);
                adsDocument.setUpdateTime(now);

                // 复制模板文件到目标位置
                String targetPath = dictService.getFileUploadPath() + filepath;
                File targetFile = FileUtil.touch(targetPath);

                // 从classpath复制模板文件
                try (var inputStream = this.getClass().getClassLoader().getResourceAsStream("template/blank_template.docx")) {
                    if (inputStream == null) {
                        throw new BizException("找不到空白模板文件！");
                    }
                    FileUtil.writeFromStream(inputStream, targetFile);
                }

                // 设置文件大小
                adsDocument.setSize(targetFile.length());

                // 保存文档记录
                adsDocumentMapper.insert(adsDocument);

                // 更新报告的docId
                report.setDocId(adsDocument.getId());
                adsDmsReportMapper.updateById(report);

            } catch (Exception e) {
                log.error("复制空白模板文件失败！", e);
                throw new BizException("创建报告文档失败！");
            }
        }
        return report;
    }

    @Override
    public void saveAlysReportPage(SaveAlysReportPageReq req) {
        int error = saveReport(req.getDocumentKey());
        if (error != 0 && error != 4) {
            throw new BizException("保存文档失败！");
        }
    }

    @Override
    public String saveAndPreviewAlysReport(SaveAndPreviewAlysReportReq req) {
        int error = saveReport(req.getDocumentKey());
        while (error == 0) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            error = saveReport(req.getDocumentKey());
        }
        if (error != 4) {
            throw new BizException("保存文档失败！");
        }
        String id = IdUtil.getSnowflakeNextIdStr();
        String path = FILE_PATH + File.separator + "tmp" + File.separator + id + ".doc";
        try {
            createReport(req.getReportTypeCode(), req.getDatekey(), path);
        } catch (IOException e) {
            log.error("预警报告预览失败！", e);
            throw new BizException("预警报告预览失败！");
        }
        return OnlyOfficeConstant.FILE_ID_TEMP + id;
    }

    @Override
    public Boolean checkReportDocExist(CreateReportDocReq req) {
        List<AdsDmsReport> list = adsDmsReportMapper.selectByIds(req.getReportTypeCodes());
        list.forEach(vo -> {
            if (StrUtil.isBlank(vo.getDocId())) {
                throw new BizException("所选内容中存在尚未配置的预警分析报告，请配置后再进行操作！");
            }
        });
        QueryWrapper<AdsMoaDiReport> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AdsMoaDiReport::getReportTypeCode, req.getReportTypeCodes())
                .eq(AdsMoaDiReport::getDatekey, req.getDatekey());
        return adsMoaDiReportMapper.selectCount(wrapper) > 0;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void createReportDoc(CreateReportDocReq req) {
        QueryWrapper<AdsMoaDiReport> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(AdsMoaDiReport::getReportTypeCode, req.getReportTypeCodes())
                .eq(AdsMoaDiReport::getDatekey, req.getDatekey());
        List<String> fileDirs = adsMoaDiReportMapper.listDiReportFileDir(req.getReportTypeCodes(), req.getDatekey());
        fileDirs.forEach(fileDir -> FileUtil.del(dictService.getFileUploadPath() + fileDir));
        adsMoaDiReportMapper.delete(wrapper);
        List<AdsDmsReport> reports = adsDmsReportMapper.selectByIds(req.getReportTypeCodes());
        reports.forEach(report -> {
            if (StrUtil.isBlank(report.getDocId())) {
                throw new BizException("所选内容中存在尚未配置的预警分析报告，请配置后再进行操作！");
            }
        });
        reports.forEach(report -> {
            AdsDocument adsDocument = new AdsDocument();
            adsDocument.setId(IdUtil.getSnowflakeNextIdStr());
            adsDocument.setTitle(report.getReportTypeName() + "_" + req.getDatekey() + ".doc");
            String filePath = FILE_PATH + File.separator + IdUtil.getSnowflakeNextIdStr() + ".doc";
            adsDocument.setPath(filePath);
            adsDocument.setCreateTime(LocalDateTime.now());
            adsDocument.setUpdateTime(LocalDateTime.now());

            AdsMoaDiReport adsMoaDiReport = new AdsMoaDiReport();
            adsMoaDiReport.setDatekey(req.getDatekey());
            adsMoaDiReport.setId(SnowflakeUtil.createId());
            adsMoaDiReport.setReportName(report.getReportTypeName());
            adsMoaDiReport.setReportTypeCode(report.getReportTypeCode());
            adsMoaDiReport.setMakeMode(req.getType());
            if (req.getType() == 1) {
                adsDocument.setCreateUser("admin");
                adsMoaDiReport.setCreateUser("admin");
            } else {
                adsDocument.setCreateUser(userSession.getSessionLoginVo().getLoginName());
                adsMoaDiReport.setCreateUser(userSession.getSessionLoginVo().getLoginName());
            }
            adsMoaDiReport.setCreateTime(LocalDateTime.now());
            adsMoaDiReport.setFileGuid(adsDocument.getId());
            String path = dictService.getFileUploadPath() + filePath;
            AdsDmsReportLog reportLog = new AdsDmsReportLog();
            reportLog.setId(SnowflakeUtil.createId());
            reportLog.setReportTypeCode(report.getReportTypeCode());
            reportLog.setDatekey(req.getDatekey());
            reportLog.setCreateTime(LocalDateTime.now());
            AdsDmsReport dmsReport = new AdsDmsReport();
            dmsReport.setReportTypeCode(report.getReportTypeCode());
            dmsReport.setReportDate(LocalDateTime.now());
            try {
                createReport(report.getReportTypeCode(), req.getDatekey(), path);

                adsDocument.setSize(new File(path).length());
                adsDocumentMapper.insert(adsDocument);

                reportLog.setReportState(1);
                reportLog.setRemark("预警报告生成成功！");
                dmsReport.setReportState(1);
            } catch (Exception e) {
                reportLog.setReportState(0);
                reportLog.setRemark("预警报告生成失败！");
                reportLog.setLogInfo(ExceptionUtil.stacktraceToOneLineString(e));
                dmsReport.setReportState(0);
                log.error("预警报告生成失败！", e);
            }
            adsDmsReportMapper.updateById(dmsReport);
            adsDmsReportLogMapper.insert(reportLog);
            adsMoaDiReportMapper.insert(adsMoaDiReport);
        });
    }

    @Override
    public String previewReportDoc(PreviewReportDocReq req) {
        AdsDmsReport report = adsDmsReportMapper.selectById(req.getReportTypeCode());
        if (StrUtil.isBlank(report.getDocId())) {
            throw new BizException("所选内容中存在尚未配置的预警分析报告，请配置后再进行操作！");
        }
        String id = IdUtil.getSnowflakeNextIdStr();
        String path = FILE_PATH + File.separator + "tmp" + File.separator + id + ".doc";
        try {
            createReport(req.getReportTypeCode(), req.getDatekey(), path);
        } catch (IOException e) {
            log.error("预警报告预览失败！", e);
            throw new BizException("预警报告预览失败！");
        }
        return OnlyOfficeConstant.FILE_ID_TEMP + id;
    }

    @Override
    public void initTask() {
        QueryWrapper<AdsDmsReport> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsReport::getCronState, 1);
        List<AdsDmsReport> reports = adsDmsReportMapper.selectList(wrapper);
        reports.forEach(this::addQuartzJob);
    }

    @Override
    public List<TreeSelectVo> listSelectOrgCodes() {
        return TreeUtils.listToTree(adsDmsReportMapper.listSelectOrgCodes(), "0");
    }

    private int saveReport(String documentKey) {
        JwtManager jwtManager = docsIntegrationSdkContext.getJwtManager();
        Map<String, Object> payload = new HashMap<>();
        payload.put("c", "forcesave");
        payload.put("key", documentKey);
        payload.put("userdata", "forcesave");
        String token = jwtManager.createToken(payload);
        String url = docsIntegrationSdkContext.getUrlManager().getDocumentServerUrl() + "/command";
        JSONObject body = new JSONObject();
        body.put("token", token);
        String response = HttpRequest.post(url)
                .header(Header.CONTENT_TYPE, ContentType.JSON.toString())
                .body(body.toString())
                .execute()
                .body();
        log.info("保存文档响应: {}", response);
        return JSONObject.parseObject(response).getIntValue("error");
    }

    /**
     * 生成预警分析报告
     *
     * @param reportTypeCode
     * @param path
     * @throws IOException
     */
    public void createReport(String reportTypeCode, String datekey, String path) throws IOException {
        AdsDmsReport report = adsDmsReportMapper.selectById(reportTypeCode);
        if (StrUtil.isBlank(report.getDocId())) {
            throw new BizException("报告文档不存在！");
        }
        AdsDocument adsDocument = adsDocumentMapper.selectById(report.getDocId());

        byte[] templateBytes = Files.readAllBytes(Paths.get(dictService.getFileUploadPath() + adsDocument.getPath()));

        // 1. 解析模板参数
        TemplateParseResult parseResult = wordTemplateEngine.parseTemplate(templateBytes);

        // 2. 分析参数类型
        ParameterAnalysisResult analysisResult = templateParameterAnalyzer.analyzeParameters(parseResult.getParameters());

        // 3. 构建填充数据（自动查询数据库）
        TemplateFillData fillData = templateFillDataBuilder.buildFillData(reportTypeCode, datekey, analysisResult);

        // 4. 生成Word文档
        byte[] bytes = wordTemplateEngine.generateDocumentWithModel(templateBytes, fillData);

        FileUtil.writeBytes(bytes, dictService.getFileUploadPath() + path);
    }

    private void addQuartzJob(AdsDmsReport report) {
        QuartzJob quartzJob = new QuartzJob();
        quartzJob.setJobKey(createAlysJobKey(report.getReportTypeCode()));
        quartzJob.setJobClass(TASK_CLASS);
        quartzJob.setStart(true);
        quartzJob.setCronExpression(report.getCronExpression());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("reportTypeCode", report.getReportTypeCode());
        quartzJob.setDataMap(dataMap);
        QuartzUtil.createScheduleJob(scheduler, quartzJob);
    }


    private String createAlysJobKey(String reportTypeCode) {
        return "alys_" + reportTypeCode;
    }
}

package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsBusinessDocument;
import com.zjhh.economy.dao.entity.AdsPmRoom;
import com.zjhh.economy.dao.entity.AdsPmRoomLabel;
import com.zjhh.economy.dao.entity.AdsPmRoomOwnership;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.enume.DmPmTypeEnum;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.AddRoomReq;
import com.zjhh.economy.request.PageRoomReq;
import com.zjhh.economy.request.UpdateRoomReq;
import com.zjhh.economy.service.RoomService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.*;
import com.zjhh.economy.vo.operationlog.RoomDetailForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/8 10:18
 */
@Service
public class RoomServiceImpl implements RoomService {

    @Resource
    private AdsPmRoomMapper adsPmRoomMapper;

    @Resource
    private UserSession userSession;

    @Resource
    private AdsPmRoomLabelMapper adsPmRoomLabelMapper;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;

    @Resource
    private AdsPmRoomOwnershipMapper adsPmRoomOwnershipMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Override
    public List<RoomStateFloorVo> listFloor(String buildingId) {
        List<RoomStateFloorVo> list = adsPmRoomMapper.listFloor(buildingId);
        list.forEach(floorVo -> {
            AtomicReference<BigDecimal> businessArea = new AtomicReference<>(BigDecimal.ZERO);
            floorVo.getRoomList().removeIf(room -> StrUtil.isBlank(room.getRoomId()));
            floorVo.getRoomList().forEach(roomVo -> businessArea.getAndSet(businessArea.get().add(roomVo.getBusinessArea())));
            floorVo.setBusinessArea(businessArea.get());
            floorVo.setRoomSize(floorVo.getRoomList().size());
        });
        return list;
    }

    @Override
    public Page<RoomVo> page(PageRoomReq req) {
        return adsPmRoomMapper.page(req.getPage(RoomVo.class), req);
    }

    @Override
    public RoomDetailVo getDetail(String roomId) {
        RoomDetailVo vo = adsPmRoomMapper.getDetail(roomId);
        QueryWrapper<AdsBusinessDocument> businessDocumentWrapper = new QueryWrapper<>();
        businessDocumentWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, roomId)
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.ROOM_IMG.value())
                .select(AdsBusinessDocument::getDocumentId)
                .orderByDesc(AdsBusinessDocument::getId)
                .last("limit 1");
        AdsBusinessDocument businessDocument = adsBusinessDocumentMapper.selectOne(businessDocumentWrapper);
        if (ObjectUtil.isNotNull(businessDocument)) {
            vo.setRoomImgId(businessDocument.getDocumentId());
        }
        vo.setLabelList(adsPmRoomMapper.listRoomLabel(roomId));
        vo.setEnterpriseHistoryList(adsPmRoomMapper.listEnterpriseHistory(roomId));

        QueryWrapper<AdsPmRoomOwnership> ownershipWrapper = new QueryWrapper<>();
        ownershipWrapper.lambda().eq(AdsPmRoomOwnership::getRoomId, roomId)
                .select(AdsPmRoomOwnership::getPhone, AdsPmRoomOwnership::getPerson)
                .orderByDesc(AdsPmRoomOwnership::getId);
        vo.setOwnershipList(BeanUtil.copyToList(adsPmRoomOwnershipMapper.selectList(ownershipWrapper), RoomDetailOwnershipVo.class));

        List<RoomDetailEnterpriseVo> enterpriseList = adsPmRoomMapper.listEnterprise(roomId);
        vo.setEnterpriseList(enterpriseList);

        boolean renovation = false;
        for (RoomDetailEnterpriseVo enterprise : enterpriseList) {
            if (ObjectUtil.isNotNull(enterprise.getRenovationStartDate()) && ObjectUtil.isNotNull(enterprise.getRenovationEndDate())
                    && !enterprise.getRenovationStartDate().isAfter(LocalDate.now()) && !enterprise.getRenovationEndDate().isBefore(LocalDate.now())) {
                renovation = true;
                break;
            }
        }
        if (!vo.getOpenHired()) {
            vo.setRoomStatusName("不可出租");
            vo.setRoomStatusCode(2);
        } else if (renovation) {
            vo.setRoomStatusName("新入驻装修中");
            vo.setRoomStatusCode(3);
        } else if (!enterpriseList.isEmpty()) {
            vo.setRoomStatusName("已入驻");
            vo.setRoomStatusCode(1);
        } else {
            vo.setRoomStatusName("空置");
            vo.setRoomStatusCode(0);
        }
        return vo;
    }

    @Override
    public AddRoomVo getAddRoom(String floorId) {
        return adsPmRoomMapper.getAddRoom(floorId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void add(AddRoomReq req) {
        AdsPmRoom room = BeanUtil.copyProperties(req, AdsPmRoom.class);
        String roomId = IdUtil.getSnowflakeNextIdStr();
        room.setId(roomId);
        room.setCreateUser(userSession.getUserCode());
        room.setCreateTime(LocalDateTime.now());
        room.setUpdateTime(LocalDateTime.now());
        adsPmRoomMapper.insert(room);
        addRoom(roomId, req);

        RoomDetailForLog dataAfter = getRoomDetailForLog(roomId);
        if (dataAfter != null) {
            LogModuleEnum moduleEnum = LogModuleEnum.ROOM;
            LogTypeEnum logTypeEnum = LogTypeEnum.ROOM_ADD;
            if (Objects.equals(req.getOptType(), "2")) {
                // 默认从房源管理中操作，传2表示从楼宇管理中操作
                moduleEnum = LogModuleEnum.BUILDING;
                logTypeEnum = LogTypeEnum.BUILDING_ROOM_ADD;
            }
            operationLogUtil.recordLog(moduleEnum, logTypeEnum, dataAfter.getId(), dataAfter.getBuildingName() + "-" + dataAfter.getRoomNo(),
                    null, JSON.toJSONString(dataAfter));
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void update(UpdateRoomReq req) {
        RoomDetailForLog dataBefore = getRoomDetailForLog(req.getRoomId());

        AdsPmRoom room = adsPmRoomMapper.selectById(req.getRoomId());
        if (ObjectUtil.isNull(room)) {
            throw new BizException("该房源不存在！");
        }
        BeanUtil.copyProperties(req, room);
        room.setUpdateTime(LocalDateTime.now());
        adsPmRoomMapper.updateById(room);
        deleteRoom(req.getRoomId());
        addRoom(req.getRoomId(), req);

        RoomDetailForLog dataAfter = getRoomDetailForLog(room.getId());
        if (dataBefore != null && dataAfter != null) {
            LogModuleEnum moduleEnum = LogModuleEnum.ROOM;
            LogTypeEnum logTypeEnum = LogTypeEnum.ROOM_EDIT;
            if (Objects.equals(req.getOptType(), "2")) {
                // 默认从房源管理中操作，传2表示从楼宇管理中操作
                moduleEnum = LogModuleEnum.BUILDING;
                logTypeEnum = LogTypeEnum.BUILDING_ROOM_EDIT;
            }
            operationLogUtil.recordLog(moduleEnum, logTypeEnum, dataBefore.getId(), dataBefore.getBuildingName() + "-" + dataBefore.getRoomNo(),
                    JSON.toJSONString(dataBefore), JSON.toJSONString(dataAfter));
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void delete(String roomId) {
        RoomDetailForLog dataBefore = getRoomDetailForLog(roomId);

        adsPmRoomMapper.deleteById(roomId);
        deleteRoom(roomId);

        if (dataBefore != null) {
            operationLogUtil.recordLog(LogModuleEnum.ROOM, LogTypeEnum.ROOM_DELETE, dataBefore.getId(), dataBefore.getBuildingName() + "-" + dataBefore.getRoomNo(),
                    JSON.toJSONString(dataBefore), null);
        }
    }

    @Override
    public RoomUpdateDetailVo getUpdateDetail(String roomId) {
        RoomUpdateDetailVo vo = adsPmRoomMapper.getUpdateDetail(roomId);
        if (ObjectUtil.isNull(vo)) {
            throw new BizException("该房源不存在！");
        }
        QueryWrapper<AdsBusinessDocument> documentWrapper = new QueryWrapper<>();
        documentWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, roomId)
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.ROOM_IMG.value())
                .select(AdsBusinessDocument::getDocumentId)
                .orderByAsc(AdsBusinessDocument::getId);
        vo.setRoomImgIds(adsBusinessDocumentMapper.selectStrings(documentWrapper));

        vo.setRoomLabels(adsPmRoomLabelMapper.listRoomLabels(roomId));

        QueryWrapper<AdsPmRoomOwnership> ownershipWrapper = new QueryWrapper<>();
        ownershipWrapper.lambda().eq(AdsPmRoomOwnership::getRoomId, roomId)
                .select(AdsPmRoomOwnership::getPerson, AdsPmRoomOwnership::getPhone)
                .orderByAsc(AdsPmRoomOwnership::getId);
        vo.setPersonList(adsPmRoomOwnershipMapper.selectList(ownershipWrapper));
        return vo;
    }

    private void deleteRoom(String roomId) {
        QueryWrapper<AdsPmRoomLabel> labelWrapper = new QueryWrapper<>();
        labelWrapper.lambda().eq(AdsPmRoomLabel::getRoomId, roomId);
        adsPmRoomLabelMapper.delete(labelWrapper);

        QueryWrapper<AdsPmRoomOwnership> ownershipWrapper = new QueryWrapper<>();
        ownershipWrapper.lambda().eq(AdsPmRoomOwnership::getRoomId, roomId);
        adsPmRoomOwnershipMapper.delete(ownershipWrapper);

        QueryWrapper<AdsBusinessDocument> documentWrapper = new QueryWrapper<>();
        documentWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, roomId)
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.ROOM_IMG.value());
        adsBusinessDocumentMapper.delete(documentWrapper);
    }

    private void addRoom(String roomId, AddRoomReq req) {
        if (CollUtil.isNotEmpty(req.getRoomLabelCodes())) {
            if (req.getRoomLabelCodes().size() > 10) {
                throw new BizException("最大只能添加10个标签！");
            }
            List<AdsPmRoomLabel> roomLabels = new ArrayList<>(req.getRoomLabelCodes().size());
            req.getRoomLabelCodes().forEach(labelCode -> {
                AdsPmRoomLabel roomLabel = new AdsPmRoomLabel();
                roomLabel.setId(IdUtil.getSnowflakeNextIdStr());
                roomLabel.setRoomId(roomId);
                roomLabel.setLabelCode(labelCode);
                roomLabels.add(roomLabel);
            });
            adsPmRoomLabelMapper.insertBatchSomeColumn(roomLabels);
        }

        if (CollUtil.isNotEmpty(req.getRoomImgIds())) {
            if (req.getRoomImgIds().size() > 3) {
                throw new BizException("最大只能添加3张图片！");
            }
            List<AdsBusinessDocument> adsBusinessDocuments = new ArrayList<>(req.getRoomImgIds().size());
            req.getRoomImgIds().forEach(roomImgId -> {
                AdsBusinessDocument adsBusinessDocument = new AdsBusinessDocument();
                adsBusinessDocument.setId(IdUtil.getSnowflakeNextIdStr());
                adsBusinessDocument.setDocumentId(roomImgId);
                adsBusinessDocument.setBusinessId(roomId);
                adsBusinessDocument.setDocumentType(DocumentTypeEnum.ROOM_IMG.value());
                adsBusinessDocuments.add(adsBusinessDocument);
            });
            adsBusinessDocumentMapper.insertBatchSomeColumn(adsBusinessDocuments);
        }

        if (CollUtil.isNotEmpty(req.getPersonList())) {
            req.getPersonList().removeIf(roomPerson -> StrUtil.isBlank(roomPerson.getPerson()) && StrUtil.isBlank(roomPerson.getPhone()));
            if (req.getPersonList().size() > 5) {
                throw new BizException("最大只能添加5个权属人！");
            }
            List<AdsPmRoomOwnership> roomOwnerships = new ArrayList<>(req.getPersonList().size());
            req.getPersonList().forEach(person -> {
                AdsPmRoomOwnership roomOwnership = new AdsPmRoomOwnership();
                roomOwnership.setId(IdUtil.getSnowflakeNextIdStr());
                roomOwnership.setRoomId(roomId);
                roomOwnership.setPerson(person.getPerson());
                roomOwnership.setPhone(person.getPhone());
                roomOwnerships.add(roomOwnership);
            });
            adsPmRoomOwnershipMapper.insertBatchSomeColumn(roomOwnerships);
        }
    }

    private RoomDetailForLog getRoomDetailForLog(String roomId) {
        RoomDetailForLog vo = changeInfoMapper.getRoomDetailForLog(roomId);
        if (vo == null) {
            return null;
        }
        vo.setRenovationName(changeInfoMapper.getDmPmName(DmPmTypeEnum.RENOVATION.code(), vo.getRenovationCode()));
        vo.setRoomTypeName(changeInfoMapper.getDmPmName(DmPmTypeEnum.ROOM_TYPE.code(), vo.getRoomTypeCode()));
        vo.setRoomImgs(changeInfoMapper.listDocTitles(vo.getId(), DocumentTypeEnum.ROOM_IMG.value()));
        vo.setPropCertName(changeInfoMapper.getDmPmName(DmPmTypeEnum.PROPERTY_CERTIFICATE.code(), vo.getPropertyCertificateCode()));
        vo.setOwnershipName(changeInfoMapper.getDmPmName(DmPmTypeEnum.OWNERSHIP.code(), vo.getOwnershipCode()));
        vo.setRoomLabels(adsPmRoomLabelMapper.listRoomLabels(roomId).stream().map(SingleSelectVo::getTitle).collect(Collectors.toList()));

        QueryWrapper<AdsPmRoomOwnership> ownershipWrapper = new QueryWrapper<>();
        ownershipWrapper.lambda().eq(AdsPmRoomOwnership::getRoomId, roomId)
                .select(AdsPmRoomOwnership::getPerson, AdsPmRoomOwnership::getPhone)
                .orderByAsc(AdsPmRoomOwnership::getId);
        vo.setPersonList(adsPmRoomOwnershipMapper.selectList(ownershipWrapper).stream().map(
                owner -> "权属人：" + owner.getPerson() + " " +
                        "联系方式：" + owner.getPhone()).collect(Collectors.toList()));
        return vo;
    }
}

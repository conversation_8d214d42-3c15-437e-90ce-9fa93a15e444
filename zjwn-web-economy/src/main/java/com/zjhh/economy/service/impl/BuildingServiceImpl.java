package com.zjhh.economy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.*;
import com.zjhh.economy.dao.mapper.*;
import com.zjhh.economy.dto.SerialNoDto;
import com.zjhh.economy.enume.BuildingExtendTypeEnum;
import com.zjhh.economy.enume.DocumentTypeEnum;
import com.zjhh.economy.enume.LogModuleEnum;
import com.zjhh.economy.enume.LogTypeEnum;
import com.zjhh.economy.request.AddBuildingReq;
import com.zjhh.economy.request.PageBuildingReq;
import com.zjhh.economy.request.UpdateBuildingReq;
import com.zjhh.economy.service.BuildingService;
import com.zjhh.economy.utils.OperationLogUtil;
import com.zjhh.economy.vo.BuildingDetailVo;
import com.zjhh.economy.vo.BuildingExtendVo;
import com.zjhh.economy.vo.BuildingProjectVo;
import com.zjhh.economy.vo.BuildingVo;
import com.zjhh.economy.vo.operationlog.BuildingDetailForLog;
import com.zjhh.user.service.impl.UserSession;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 16:58
 */
@Service
public class BuildingServiceImpl extends BaseProjectServiceImpl implements BuildingService {

    @Resource
    private AdsPmBuildingMapper adsPmBuildingMapper;

    @Resource
    private AdsPmBuildingExtendMapper adsPmBuildingExtendMapper;

    @Resource
    private AdsBusinessDocumentMapper adsBusinessDocumentMapper;

    @Resource
    private AdsPmProjectMapper adsPmProjectMapper;

    @Resource
    private AdsDmsColumnsMapper adsDmsColumnsMapper;

    @Resource
    private ChangeInfoMapper changeInfoMapper;

    @Resource
    private OperationLogUtil operationLogUtil;

    @Resource
    private UserSession userSession;

    @Override
    public Page<BuildingVo> page(PageBuildingReq req) {
        return adsPmBuildingMapper.page(req.getPage(BuildingVo.class), req);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void add(AddBuildingReq req) {
        checkExistBuildName(req.getBuildingName(), null);
        AdsPmBuilding adsPmBuilding = BeanUtil.copyProperties(req, AdsPmBuilding.class);
        String buildingId = IdUtil.getSnowflakeNextIdStr();
        adsPmBuilding.setId(buildingId);

        QueryWrapper<AdsPmProject> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsPmProject::getId, req.getProjectId())
                .select(AdsPmProject::getSerialNo);
        AdsPmProject project = adsPmProjectMapper.selectOne(wrapper);
        SerialNoDto serialNoDto = getBuildingSerialNo(project.getSerialNo());
        if (StrUtil.isBlank(adsPmBuilding.getSerialNo())) {
            adsPmBuilding.setSerialNo(serialNoDto.getSerialNo());
        }
        adsPmBuilding.setXh(serialNoDto.getXh());
        adsPmBuilding.setCreateUser(userSession.getUserCode());
        adsPmBuilding.setCreateTime(LocalDateTime.now());
        adsPmBuilding.setUpdateTime(LocalDateTime.now());
        adsPmBuildingMapper.insert(adsPmBuilding);

        addBuilding(req, buildingId);

        BuildingDetailForLog dataAfter = getDetailForLog(buildingId);
        if (dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.BUILDING, LogTypeEnum.BUILDING_ADD, dataAfter.getId(), dataAfter.getBuildingName(),
                    null, JSON.toJSONString(dataAfter));
        }
    }

    @Override
    public void update(UpdateBuildingReq req) {
        BuildingDetailForLog dataBefore = getDetailForLog(req.getBuildingId());

        checkExistBuildName(req.getBuildingName(), req.getBuildingId());
        AdsPmBuilding adsPmBuilding = adsPmBuildingMapper.selectById(req.getBuildingId());
        if (ObjectUtil.isNull(adsPmBuilding)) {
            throw new BizException("该楼宇不存在！");
        }
        BeanUtil.copyProperties(req, adsPmBuilding);
        adsPmBuilding.setUpdateTime(LocalDateTime.now());
        if (StrUtil.isBlank(adsPmBuilding.getSerialNo())) {
            QueryWrapper<AdsPmProject> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(AdsPmProject::getId, req.getProjectId())
                    .select(AdsPmProject::getSerialNo);
            AdsPmProject project = adsPmProjectMapper.selectOne(wrapper);
            adsPmBuilding.setSerialNo(getBuildingSerialNo(project.getSerialNo(), adsPmBuilding.getXh()));
        }
        adsPmBuildingMapper.updateById(adsPmBuilding);

        deleteBuilding(req.getBuildingId());

        addBuilding(req, req.getBuildingId());

        BuildingDetailForLog dataAfter = getDetailForLog(req.getBuildingId());
        if (dataBefore != null && dataAfter != null) {
            operationLogUtil.recordLog(LogModuleEnum.BUILDING, LogTypeEnum.BUILDING_EDIT, dataBefore.getId(), dataBefore.getBuildingName(),
                    JSON.toJSONString(dataBefore), JSON.toJSONString(dataAfter));
        }
    }

    @Override
    public void delete(String buildingId) {
        BuildingDetailForLog dataBefore = getDetailForLog(buildingId);
        if (adsPmBuildingMapper.countRoomSize(buildingId) > 0) {
            throw new BizException("该楼宇下已存在房间信息，不可删除！");
        }
        adsPmBuildingMapper.deleteById(buildingId);

        deleteBuilding(buildingId);

        if (dataBefore != null) {
            operationLogUtil.recordLog(LogModuleEnum.BUILDING, LogTypeEnum.BUILDING_DELETE, dataBefore.getId(), dataBefore.getBuildingName(),
                    JSON.toJSONString(dataBefore), null);
        }
    }

    @Override
    public List<AdsDmsColumns> listShowColumn() {
        QueryWrapper<AdsDmsColumns> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(AdsDmsColumns::getShowed, true)
                .eq(AdsDmsColumns::getTableType, "building")
                .select(AdsDmsColumns::getColumnId, AdsDmsColumns::getColumnName)
                .orderByAsc(AdsDmsColumns::getSort);
        return adsDmsColumnsMapper.selectList(wrapper);
    }

    @Override
    public List<BuildingProjectVo> listBuildingProject() {
        return adsPmBuildingMapper.listBuildingProject();
    }

    @Override
    public BuildingDetailVo getDetail(String buildingId) {
        BuildingDetailVo vo = adsPmBuildingMapper.getDetail(buildingId);
        if (ObjectUtil.isNull(vo)) {
            throw new BizException("该楼宇信息不存在！");
        }
        List<BuildingExtendVo> list = adsPmBuildingExtendMapper.listBuildingExtend(buildingId);
        if (CollUtil.isNotEmpty(list)) {
            List<SingleSelectVo> labelNames = new ArrayList<>();
            List<SingleSelectVo> positionNames = new ArrayList<>();
            List<SingleSelectVo> configNames = new ArrayList<>();
            list.forEach(extend -> {
                SingleSelectVo singleSelectVo = new SingleSelectVo();
                singleSelectVo.setCode(extend.getExtendCode());
                if (extend.getType().equals(BuildingExtendTypeEnum.LABEL.code())) {
                    singleSelectVo.setTitle(extend.getLabelName());
                    labelNames.add(singleSelectVo);
                } else if (extend.getType().equals(BuildingExtendTypeEnum.POSITION.code())) {
                    singleSelectVo.setTitle(extend.getPositionName());
                    positionNames.add(singleSelectVo);
                } else {
                    singleSelectVo.setTitle(extend.getConfigName());
                    configNames.add(singleSelectVo);
                }
            });
            vo.setLabelNames(labelNames);
            vo.setPositionNames(positionNames);
            vo.setConfigNames(configNames);
        }
        return vo;
    }

    private BuildingDetailForLog getDetailForLog(String buildingId) {
        BuildingDetailForLog vo = changeInfoMapper.getBuildingDetailForLog(buildingId);
        if (ObjectUtil.isNull(vo)) {
            return null;
        }
        List<BuildingExtendVo> list = adsPmBuildingExtendMapper.listBuildingExtend(buildingId);
        if (CollUtil.isNotEmpty(list)) {
            List<String> labelNames = new ArrayList<>();
            List<String> positionNames = new ArrayList<>();
            List<String> configNames = new ArrayList<>();
            list.forEach(extend -> {
                if (extend.getType().equals(BuildingExtendTypeEnum.LABEL.code())) {
                    labelNames.add(extend.getLabelName());
                } else if (extend.getType().equals(BuildingExtendTypeEnum.POSITION.code())) {
                    positionNames.add(extend.getPositionName());
                } else {
                    configNames.add(extend.getConfigName());
                }
            });
            vo.setLabelNames(labelNames);
            vo.setPositionNames(positionNames);
            vo.setConfigNames(configNames);
        }
        return vo;
    }

    private void checkExistBuildName(String buildingName, String buildingId) {
        QueryWrapper<AdsPmBuilding> buildWrapper = new QueryWrapper<>();
        buildWrapper.lambda().eq(AdsPmBuilding::getBuildingName, buildingName)
                .ne(StrUtil.isNotBlank(buildingId), AdsPmBuilding::getId, buildingId);
        if (adsPmBuildingMapper.exists(buildWrapper)) {
            throw new BizException("该楼宇名称已存在！");
        }
    }

    private void deleteBuilding(String buildingId) {
        QueryWrapper<AdsPmBuildingExtend> extendWrapper = new QueryWrapper<>();
        extendWrapper.lambda().eq(AdsPmBuildingExtend::getBuildingId, buildingId);
        adsPmBuildingExtendMapper.delete(extendWrapper);

        QueryWrapper<AdsBusinessDocument> documentWrapper = new QueryWrapper<>();
        documentWrapper.lambda().eq(AdsBusinessDocument::getBusinessId, buildingId)
                .eq(AdsBusinessDocument::getDocumentType, DocumentTypeEnum.BUILDING_OTHER_IMG.value());
        adsBusinessDocumentMapper.delete(documentWrapper);
    }

    private void addBuilding(AddBuildingReq req, String buildingId) {
        List<AdsPmBuildingExtend> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(req.getPositionCodes())) {
            req.getPositionCodes().forEach(positionCode -> {
                AdsPmBuildingExtend extend = new AdsPmBuildingExtend();
                extend.setId(IdUtil.getSnowflakeNextIdStr());
                extend.setBuildingId(buildingId);
                extend.setExtendCode(positionCode);
                extend.setType(BuildingExtendTypeEnum.POSITION.code());
                list.add(extend);
            });
        }
        if (CollUtil.isNotEmpty(req.getLabelCodes())) {
            if (req.getLabelCodes().size() > 10) {
                throw new BizException("最大只能添加10个标签！");
            }
            req.getLabelCodes().forEach(labelCode -> {
                AdsPmBuildingExtend extend = new AdsPmBuildingExtend();
                extend.setId(IdUtil.getSnowflakeNextIdStr());
                extend.setBuildingId(buildingId);
                extend.setExtendCode(labelCode);
                extend.setType(BuildingExtendTypeEnum.LABEL.code());
                list.add(extend);
            });
        }
        if (CollUtil.isNotEmpty(req.getConfigCodes())) {
            req.getConfigCodes().forEach(configCode -> {
                AdsPmBuildingExtend extend = new AdsPmBuildingExtend();
                extend.setId(IdUtil.getSnowflakeNextIdStr());
                extend.setBuildingId(buildingId);
                extend.setExtendCode(configCode);
                extend.setType(BuildingExtendTypeEnum.CONFIG.code());
                list.add(extend);
            });
        }
        if (CollUtil.isNotEmpty(list)) {
            adsPmBuildingExtendMapper.insertBatchSomeColumn(list);
        }
    }
}

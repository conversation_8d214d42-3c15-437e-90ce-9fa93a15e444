package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 楼宇表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_building")
public class AdsPmBuilding implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 编号
     */
    private String serialNo;

    /**
     * 楼宇名称
     */
    private String buildingName;

    private String projectId;

    /**
     * 建筑面积
     */
    private BigDecimal buildingArea;

    /**
     * 商务面积
     */
    private BigDecimal businessArea;

    /**
     * 楼宇投入运行时间
     */
    private LocalDate operationTime;

    /**
     * 楼宇状态
     */
    private String buildingStatusCode;

    /**
     * 楼宇类型
     */
    private String buildingTypeCode;

    /**
     * 负责人
     */
    private String head;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 楼宇简介
     */
    private String introduce;

    /**
     * 地上停车位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer landParkSpace;

    /**
     * 地下停车位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer undergroundParkSpace;

    /**
     * 序号
     */
    private Integer xh;

    /**
     * 楼宇外观图
     */
    private String outsideImgId;

    /**
     * 楼宇其他图片
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String otherImgId;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}

package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 企业信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_enterprise")
public class AdsPmEnterprise implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 社会信用代码
     */
    private String uscc;

    /**
     * 曾用名
     */
    private String oldName;

    /**
     * 成立日期
     */
    private LocalDate foundDate;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 企业类型
     */
    private String enterpriseTypeCode;

    /**
     * 行业
     */
    private String industryCode;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     *  货币类型
     */
    private Integer currencyType;

    /**
     * 住所
     */
    private String residence;

    /**
     * 电话
     */
    private String phone;

    /**
     * 是否规上企业
     */
    private Boolean onScaled;

    /**
     * 是否属地企业
     */
    private Boolean territorialized;

    /**
     * 营业期限开始时间
     */
    private LocalDate businessTermStart;

    /**
     * 营业期限结束时间
     */
    private LocalDate businessTermEnd;

    /**
     * 融资阶段
     */
    private String financingStageCode;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 企业联系人
     */
    private String entContactPerson;

    /**
     * 企业联系电话
     */
    private String entPhone;

    /**
     * 企业logo
     */
    private String logoImgId;

    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.INSERT)
    private String serialNo;


}

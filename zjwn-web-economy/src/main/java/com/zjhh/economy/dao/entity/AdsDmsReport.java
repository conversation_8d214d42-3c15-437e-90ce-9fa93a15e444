package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 智能预警分析报告
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_dms_report")
public class AdsDmsReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报告编码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String reportTypeCode;

    /**
     * 报告名称
     */
    private String reportTypeName;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最新生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportDate;

    /**
     * 最新生成状态（0-失败；1-成功）
     */
    private Integer reportState;

    /**
     * cron表达式
     */
    private String cronExpression;

    /**
     * 描述
     */
    private String description;

    /**
     * 定时任务状态 1-正常 0-停止
     */
    private Integer cronState;

    private String docId;

    private Integer authType;
}

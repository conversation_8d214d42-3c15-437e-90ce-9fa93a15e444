package com.zjhh.economy.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 房源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_pm_room")
public class AdsPmRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String floorId;

    /**
     * 房号
     */
    private String roomNo;

    /**
     * 建筑面积
     */
    private BigDecimal buildingArea;

    /**
     * 商务面积
     */
    private BigDecimal businessArea;

    /**
     * 是否开放出租
     */
    private Boolean openHired;

    /**
     * 装修状态编码
     */
    private String renovationCode;

    /**
     * 房间类型编码
     */
    private String roomTypeCode;

    /**
     * 租金单价
     */
    private BigDecimal rendUnitPrice;

    /**
     * 租金单价单位
     */
    private Integer rendUnit;

    /**
     * 物业费单价
     */
    private BigDecimal propertyFeesUnitPrice;

    /**
     * 物业费单价单位
     */
    private Integer propertyFeesUnit;

    /**
     * 水费单价
     */
    private BigDecimal waterFeesUnitPrice;

    /**
     * 电费单价
     */
    private BigDecimal electricityFeesUnitPrice;

    /**
     * 房屋朝向
     */
    private Integer houseOrientation;

    /**
     * 权属性质编码
     */
    private String ownershipCode;

    /**
     * 产证状态编码
     */
    private String propertyCertificateCode;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;


}

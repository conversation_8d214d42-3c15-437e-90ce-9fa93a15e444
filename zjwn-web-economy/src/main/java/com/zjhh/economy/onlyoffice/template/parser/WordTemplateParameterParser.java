package com.zjhh.economy.onlyoffice.template.parser;

import com.aspose.words.*;
import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import com.zjhh.economy.onlyoffice.template.model.ParameterType;
import com.zjhh.economy.onlyoffice.template.model.TemplateParameter;
import com.zjhh.economy.onlyoffice.template.model.TemplateParseResult;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word模板参数解析核心类
 * 负责解析Word文档中的{{}}参数标记
 */
@Slf4j
public class WordTemplateParameterParser {
    
    private static final Pattern SIMPLE_PARAM_PATTERN = Pattern.compile("\\{\\{([^#/][^}]*)\\}\\}");
    
    /**
     * 解析模板文档，提取所有参数
     * @param document Aspose Word文档对象
     * @return 参数解析结果
     */
    public TemplateParseResult parseTemplate(Document document) {
        try {
            List<TemplateParameter> parameters = new ArrayList<>();
            
            // 解析文本参数
            extractTextParameters(document, parameters);
            
            // 解析表格参数
            extractTableParameters(document, parameters);
            
            // 解析图表参数
            extractChartParameters(document, parameters);
            
            log.info("模板解析完成，共发现{}个参数", parameters.size());
            return new TemplateParseResult(parameters);
            
        } catch (Exception e) {
            log.error("模板解析失败", e);
            throw new TemplateProcessException("模板解析失败", e);
        }
    }
    
    /**
     * 提取文本参数 - 只处理不在表格和图表中的段落参数
     */
    private void extractTextParameters(Document document, List<TemplateParameter> parameters) throws Exception {
        NodeCollection<Paragraph> allParagraphs = document.getChildNodes(NodeType.PARAGRAPH, true);
        
        for (Paragraph paragraph : allParagraphs) {
            // 检查段落是否在表格中
            if (isInTable(paragraph)) {
                continue; // 跳过表格中的段落，这些由表格参数处理
            }
            
            // 检查段落是否在图表中
            if (isInChart(paragraph)) {
                continue; // 跳过图表中的段落，这些由图表参数处理
            }
            
            String text = paragraph.getText();
            Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(text);
            
            while (matcher.find()) {
                String paramName = matcher.group(1).trim();
                if (!isDuplicate(parameters, paramName)) {
                    parameters.add(TemplateParameter.builder()
                            .name(paramName)
                            .type(ParameterType.TEXT)
                            .location("段落")
                            .build());
                    log.debug("发现文本参数: {}", paramName);
                }
            }
        }
    }
    
    /**
     * 检查段落是否在表格中
     */
    private boolean isInTable(Paragraph paragraph) {
        Node parent = paragraph.getParentNode();
        while (parent != null) {
            if (parent.getNodeType() == NodeType.TABLE) {
                return true;
            }
            parent = parent.getParentNode();
        }
        return false;
    }
    
    /**
     * 检查段落是否在图表中
     */
    private boolean isInChart(Paragraph paragraph) {
        Node parent = paragraph.getParentNode();
        while (parent != null) {
            if (parent.getNodeType() == NodeType.SHAPE) {
                Shape shape = (Shape) parent;
                if (shape.hasChart()) {
                    return true;
                }
            }
            parent = parent.getParentNode();
        }
        return false;
    }
    
    /**
     * 提取表格参数 - 基于位置识别，不依赖参数名称格式
     */
    private void extractTableParameters(Document document, List<TemplateParameter> parameters) throws Exception {
        NodeCollection<Table> tables = document.getChildNodes(NodeType.TABLE, true);
        
        for (Table table : tables) {
            Set<String> tableParams = new HashSet<>();
            
            for (Row row : table.getRows()) {
                for (Cell cell : row.getCells()) {
                    String cellText = cell.getText();
                    Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(cellText);
                    
                    while (matcher.find()) {
                        String paramName = matcher.group(1).trim();
                        tableParams.add(paramName);
                    }
                }
            }
            
            // 为表格中发现的每个参数创建TABLE类型参数
            for (String paramName : tableParams) {
                if (!isDuplicate(parameters, paramName)) {
                    parameters.add(TemplateParameter.builder()
                            .name(paramName)
                            .type(ParameterType.TABLE)
                            .location("表格")
                            .build());
                    log.debug("发现表格参数: {}", paramName);
                }
            }
        }
    }
    
    /**
     * 提取图表参数 - 基于位置识别，不依赖参数名称格式
     */
    private void extractChartParameters(Document document, List<TemplateParameter> parameters) throws Exception {
        NodeCollection<Shape> shapes = document.getChildNodes(NodeType.SHAPE, true);
        
        for (Shape shape : shapes) {
            if (shape.hasChart()) {
                Chart chart = shape.getChart();
                Set<String> chartParams = new HashSet<>();
                
                // 遍历图表的数据序列，查找参数标记
                for (Object seriesObj : chart.getSeries()) {
                    if (seriesObj instanceof ChartSeries) {
                        ChartSeries series = (ChartSeries) seriesObj;
                        String seriesName = series.getName();
                        
                        // 检查序列名称中的所有参数
                        Matcher matcher = SIMPLE_PARAM_PATTERN.matcher(seriesName);
                        while (matcher.find()) {
                            String paramName = matcher.group(1).trim();
                            chartParams.add(paramName);
                        }
                    }
                }
                
                // 也检查图表标题或其他文本位置的参数
                String shapeText = shape.getText();
                Matcher textMatcher = SIMPLE_PARAM_PATTERN.matcher(shapeText);
                while (textMatcher.find()) {
                    String paramName = textMatcher.group(1).trim();
                    chartParams.add(paramName);
                }
                
                // 为图表中发现的每个参数创建CHART类型参数
                for (String paramName : chartParams) {
                    if (!isDuplicate(parameters, paramName)) {
                        parameters.add(TemplateParameter.builder()
                                .name(paramName)
                                .type(ParameterType.CHART)
                                .location("图表")
                                .build());
                        log.debug("发现图表参数: {}", paramName);
                    }
                }
            }
        }
    }
    
    private boolean isDuplicate(List<TemplateParameter> parameters, String name) {
        return parameters.stream().anyMatch(p -> name.equals(p.getName()));
    }
}
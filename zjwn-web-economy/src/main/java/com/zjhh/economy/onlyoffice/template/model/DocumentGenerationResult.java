package com.zjhh.economy.onlyoffice.template.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 文档生成结果
 */
@Data
@AllArgsConstructor
public class DocumentGenerationResult {
    private TemplateParseResult parseResult;    // 参数解析结果
    private byte[] documentBytes;               // 生成的文档字节数组
    
    /**
     * 获取参数列表
     */
    public List<TemplateParameter> getParameters() {
        return parseResult.getParameters();
    }
    
    /**
     * 获取文档大小
     */
    public long getDocumentSize() {
        return documentBytes.length;
    }
}
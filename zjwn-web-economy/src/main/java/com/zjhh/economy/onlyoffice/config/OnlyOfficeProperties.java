package com.zjhh.economy.onlyoffice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * OnlyOffice配置属性类
 *
 * <AUTHOR>
 * @since 2025/8/14 11:44
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "onlyoffice")
public class OnlyOfficeProperties {

    /**
     * OnlyOffice服务器地址
     */
    private String onlyOfficeUrl;

    /**
     * 安全配置
     */
    private String onlyOfficeSecurityKey;

    /**
     * 当前服务的地址
     */
    private String serverUrl;
}

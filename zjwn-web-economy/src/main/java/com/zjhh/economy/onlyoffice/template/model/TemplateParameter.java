package com.zjhh.economy.onlyoffice.template.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 模板参数定义
 */
@Data
@Builder
public class TemplateParameter {
    private String name;                    // 参数名
    private ParameterType type;             // 参数类型
    private String location;                // 参数位置描述
    private List<String> fields;            // 表格参数的字段列表
    private String description;             // 参数描述
}
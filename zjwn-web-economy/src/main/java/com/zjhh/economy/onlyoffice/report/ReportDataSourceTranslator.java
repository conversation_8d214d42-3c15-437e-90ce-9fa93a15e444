package com.zjhh.economy.onlyoffice.report;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zjhh.economy.dao.entity.AdsDmsReportDataColumnSet;
import com.zjhh.economy.dao.entity.AdsDmsReportDataSet;
import com.zjhh.economy.dao.mapper.AdsDmsReportDataColumnSetMapper;
import com.zjhh.economy.dao.mapper.AdsDmsReportDataSetMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源翻译服务 - 将业务名称转换为编码
 */
@Slf4j
@Service
public class ReportDataSourceTranslator {

    @Resource
    private AdsDmsReportDataSetMapper dataSetMapper;

    @Resource
    private AdsDmsReportDataColumnSetMapper columnSetMapper;

    private final Map<String, String> dataSourceCache = new ConcurrentHashMap<>();
    private final Map<String, Map<String, String>> columnCache = new ConcurrentHashMap<>();

    /**
     * 根据业务数据名称获取数据源编码
     *
     * @param dataSetName 业务数据名称
     * @return 数据源编码
     */
    @Cacheable(value = "dataSourceTranslate", key = "#dataSetName")
    public String translateDataSourceName(String dataSetName) {
        if (!StringUtils.hasText(dataSetName)) {
            log.warn("数据源名称为空");
            return null;
        }

        try {
            String cached = dataSourceCache.get(dataSetName);
            if (cached != null) {
                return cached;
            }

            LambdaQueryWrapper<AdsDmsReportDataSet> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AdsDmsReportDataSet::getDataSetName, dataSetName);

            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(wrapper);
            if (dataSet != null) {
                String dataTypeCode = dataSet.getDataTypeCode();
                dataSourceCache.put(dataSetName, dataTypeCode);
                log.debug("数据源翻译成功: {} -> {}", dataSetName, dataTypeCode);
                return dataTypeCode;
            }

            log.warn("未找到数据源: {}", dataSetName);
            return null;

        } catch (Exception e) {
            log.error("数据源翻译失败: {}", dataSetName, e);
            return null;
        }
    }

    /**
     * 根据数据源和字段名称获取字段编码
     *
     * @param dataTypeCode 数据源编码
     * @param columnName   字段名称
     * @return 字段编码
     */
    @Cacheable(value = "columnTranslate", key = "#dataTypeCode + '_' + #columnName")
    public String translateColumnName(String dataTypeCode, String columnName) {
        if (!StringUtils.hasText(dataTypeCode) || !StringUtils.hasText(columnName)) {
            log.warn("数据源编码或字段名称为空: dataTypeCode={}, columnName={}", dataTypeCode, columnName);
            return null;
        }

        try {
            Map<String, String> columnMap = columnCache.get(dataTypeCode);
            if (columnMap != null) {
                String cached = columnMap.get(columnName);
                if (cached != null) {
                    return cached;
                }
            }

            // 首先获取数据源ID
            LambdaQueryWrapper<AdsDmsReportDataSet> dataSetWrapper = new LambdaQueryWrapper<>();
            dataSetWrapper.eq(AdsDmsReportDataSet::getDataTypeCode, dataTypeCode);
            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(dataSetWrapper);

            if (dataSet == null) {
                log.warn("未找到数据源: {}", dataTypeCode);
                return null;
            }

            // 查询字段编码
            LambdaQueryWrapper<AdsDmsReportDataColumnSet> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(AdsDmsReportDataColumnSet::getDataSetId, dataSet.getId())
                    .eq(AdsDmsReportDataColumnSet::getColumnName, columnName);

            AdsDmsReportDataColumnSet columnSet = columnSetMapper.selectOne(columnWrapper);
            if (columnSet != null) {
                String columnKey = columnSet.getColumnKey();

                // 缓存结果
                columnMap = columnCache.computeIfAbsent(dataTypeCode, k -> new ConcurrentHashMap<>());
                columnMap.put(columnName, columnKey);

                log.debug("字段翻译成功: {}.{} -> {}", dataTypeCode, columnName, columnKey);
                return columnKey;
            }

            log.warn("未找到字段: {}.{}", dataTypeCode, columnName);
            return null;

        } catch (Exception e) {
            log.error("字段翻译失败: {}.{}", dataTypeCode, columnName, e);
            return null;
        }
    }

    /**
     * 批量翻译字段名称
     *
     * @param dataTypeCode 数据源编码
     * @param columnNames  字段名称列表
     * @return 字段名称->字段编码映射
     */
    public Map<String, String> translateColumnNames(String dataTypeCode, List<String> columnNames) {
        Map<String, String> result = new HashMap<>();

        if (!StringUtils.hasText(dataTypeCode) || columnNames == null || columnNames.isEmpty()) {
            return result;
        }

        try {
            // 首先获取数据源ID
            LambdaQueryWrapper<AdsDmsReportDataSet> dataSetWrapper = new LambdaQueryWrapper<>();
            dataSetWrapper.eq(AdsDmsReportDataSet::getDataTypeCode, dataTypeCode);
            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(dataSetWrapper);

            if (dataSet == null) {
                log.warn("未找到数据源: {}", dataTypeCode);
                return result;
            }

            // 批量查询字段编码
            LambdaQueryWrapper<AdsDmsReportDataColumnSet> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(AdsDmsReportDataColumnSet::getDataSetId, dataSet.getId())
                    .in(AdsDmsReportDataColumnSet::getColumnName, columnNames);

            List<AdsDmsReportDataColumnSet> columnSets = columnSetMapper.selectList(columnWrapper);

            // 构建映射关系并缓存
            Map<String, String> columnMap = columnCache.computeIfAbsent(dataTypeCode, k -> new ConcurrentHashMap<>());
            for (AdsDmsReportDataColumnSet columnSet : columnSets) {
                String columnName = columnSet.getColumnName();
                String columnKey = columnSet.getColumnKey();
                result.put(columnName, columnKey);
                columnMap.put(columnName, columnKey);
            }

            log.debug("批量字段翻译完成: {} -> {} 个字段", dataTypeCode, result.size());
            return result;

        } catch (Exception e) {
            log.error("批量字段翻译失败: {}", dataTypeCode, e);
            return result;
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        dataSourceCache.clear();
        columnCache.clear();
        log.info("翻译缓存已清除");
    }

    /**
     * 预加载指定数据源的字段映射
     *
     * @param dataTypeCode 数据源编码
     */
    public void preloadColumnMapping(String dataTypeCode) {
        try {
            LambdaQueryWrapper<AdsDmsReportDataSet> dataSetWrapper = new LambdaQueryWrapper<>();
            dataSetWrapper.eq(AdsDmsReportDataSet::getDataTypeCode, dataTypeCode);
            AdsDmsReportDataSet dataSet = dataSetMapper.selectOne(dataSetWrapper);

            if (dataSet == null) {
                return;
            }

            LambdaQueryWrapper<AdsDmsReportDataColumnSet> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(AdsDmsReportDataColumnSet::getDataSetId, dataSet.getId());
            List<AdsDmsReportDataColumnSet> columnSets = columnSetMapper.selectList(columnWrapper);

            Map<String, String> columnMap = new ConcurrentHashMap<>();
            for (AdsDmsReportDataColumnSet columnSet : columnSets) {
                columnMap.put(columnSet.getColumnName(), columnSet.getColumnKey());
            }

            columnCache.put(dataTypeCode, columnMap);
            log.info("预加载字段映射完成: {} -> {} 个字段", dataTypeCode, columnMap.size());

        } catch (Exception e) {
            log.error("预加载字段映射失败: {}", dataTypeCode, e);
        }
    }
}
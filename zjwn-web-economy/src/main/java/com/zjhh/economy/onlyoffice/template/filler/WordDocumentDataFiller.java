package com.zjhh.economy.onlyoffice.template.filler;

import com.aspose.words.*;
import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word文档数据填充核心类
 * 负责将数据填充到模板中替换参数
 */
@Slf4j
public class WordDocumentDataFiller {
    
    /**
     * 填充数据到Word文档
     * @param document Aspose Word文档对象
     * @param data 要填充的数据
     * @return 填充完成的文档字节数组
     */
    public byte[] fillData(Document document, Map<String, Object> data) {
        try {
            log.info("开始填充文档数据，数据项数量: {}", data.size());
            
            // 1. 替换文本参数
            replaceTextParameters(document, data);
            
            // 2. 处理表格参数
            processTableParameters(document, data);
            
            // 3. 处理图表参数
            processChartParameters(document, data);
            
            // 4. 转换为字节数组
            byte[] result = documentToBytes(document);
            log.info("文档数据填充完成，生成文档大小: {} bytes", result.length);
            return result;
            
        } catch (Exception e) {
            log.error("文档数据填充失败", e);
            throw new TemplateProcessException("文档数据填充失败", e);
        }
    }
    
    /**
     * 替换文本参数
     */
    private void replaceTextParameters(Document document, Map<String, Object> data) throws Exception {
        Pattern pattern = Pattern.compile("\\{\\{([^#/][^}]*)\\}\\}");
        
        NodeCollection<Paragraph> paragraphs = document.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph paragraph : paragraphs) {
            Range range = paragraph.getRange();
            String text = range.getText();
            
            if (pattern.matcher(text).find()) {
                String newText = text;
                Matcher matcher = pattern.matcher(text);
                
                while (matcher.find()) {
                    String param = matcher.group(1).trim();
                    // 只处理文本参数，排除表格和图表参数
                    if (!param.contains(".") && !param.matches("x\\d+")) {
                        Object val = getNestedValue(data, param);
                        String replacement = formatValue(val);
                        newText = newText.replace(matcher.group(0), replacement);
                        log.debug("替换文本参数: {} -> {}", param, replacement);
                    }
                }
                
                if (!newText.equals(text)) {
                    // 直接替换文本内容
                    range.replace(pattern.pattern(), newText, new FindReplaceOptions());
                }
            }
        }
    }
    
    /**
     * 处理表格参数 - 支持{{product.name}}格式
     */
    private void processTableParameters(Document document, Map<String, Object> data) throws Exception {
        NodeCollection<Table> tables = document.getChildNodes(NodeType.TABLE, true);
        
        for (Table table : tables) {
            processTableObjectRows(table, data);
        }
    }
    
    /**
     * 处理包含对象参数的表格行
     */
    private void processTableObjectRows(Table table, Map<String, Object> data) throws Exception {
        Pattern objectParamPattern = Pattern.compile("\\{\\{(\\w+)\\.(\\w+)\\}\\}");
        ArrayList<Row> rowsToProcess = new ArrayList<>();
        
        // 找到包含对象参数的行
        for (Row row : table.getRows()) {
            String rowText = row.getText();
            if (objectParamPattern.matcher(rowText).find()) {
                rowsToProcess.add(row);
            }
        }
        
        // 处理每个包含对象参数的行
        for (Row templateRow : rowsToProcess) {
            String rowText = templateRow.getText();
            String objectName = extractObjectName(rowText);
            
            if (objectName != null && data.containsKey(objectName)) {
                Object objectData = data.get(objectName);
                
                if (objectData instanceof java.util.List) {
                    java.util.List<?> items = (java.util.List<?>) objectData;
                    log.debug("处理表格参数: {}, 数据行数: {}", objectName, items.size());
                    
                    // 在模板行位置插入数据行
                    int insertIndex = table.indexOf(templateRow);
                    
                    for (int i = 0; i < items.size(); i++) {
                        Object item = items.get(i);
                        
                        // 克隆模板行
                        Row newRow = (Row) templateRow.deepClone(true);
                        
                        // 替换对象参数
                        replaceObjectParameters(newRow, objectName, item);
                        
                        // 插入新行
                        if (i == 0) {
                            table.insertAfter(newRow, templateRow);
                        } else {
                            table.insertAfter(newRow, table.getRows().get(insertIndex + i));
                        }
                    }
                    
                    // 删除模板行
                    templateRow.remove();
                }
            }
        }
    }
    
    /**
     * 提取对象名称
     */
    private String extractObjectName(String rowText) {
        Pattern pattern = Pattern.compile("\\{\\{(\\w+)\\.\\w+\\}\\}");
        Matcher matcher = pattern.matcher(rowText);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 替换对象参数
     */
    private void replaceObjectParameters(Row row, String objectName, Object item) throws Exception {
        Pattern pattern = Pattern.compile("\\{\\{" + objectName + "\\.(\\w+)\\}\\}");
        
        for (Cell cell : row.getCells()) {
            Range range = cell.getRange();
            String cellText = range.getText();
            
            if (pattern.matcher(cellText).find()) {
                String newText = cellText;
                Matcher matcher = pattern.matcher(cellText);
                
                while (matcher.find()) {
                    String fieldName = matcher.group(1);
                    Object value = getObjectFieldValue(item, fieldName);
                    String replacement = formatValue(value);
                    newText = newText.replace(matcher.group(0), replacement);
                }
                
                // 使用简单的文本替换
                range.replace(pattern.pattern(), newText, new FindReplaceOptions());
            }
        }
    }
    
    /**
     * 获取对象字段值
     */
    private Object getObjectFieldValue(Object item, String fieldName) {
        if (item instanceof Map) {
            return ((Map<?, ?>) item).get(fieldName);
        } else {
            // 通过反射获取字段值
            try {
                Field field = item.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(item);
            } catch (Exception e) {
                log.warn("获取对象字段值失败: {}.{}", item.getClass().getSimpleName(), fieldName);
                return null;
            }
        }
    }
    
    /**
     * 处理图表参数 - 支持完整图表数据替换
     */
    private void processChartParameters(Document document, Map<String, Object> data) throws Exception {
        NodeCollection<Shape> shapes = document.getChildNodes(NodeType.SHAPE, true);
        Pattern chartParamPattern = Pattern.compile("\\{\\{(x\\d+)\\}\\}");
        
        for (Shape shape : shapes) {
            if (shape.hasChart()) {
                Chart chart = shape.getChart();
                Set<String> chartParams = new HashSet<>();
                
                // 收集图表中的所有参数
                for (Object seriesObj : chart.getSeries()) {
                    if (seriesObj instanceof ChartSeries) {
                        ChartSeries series = (ChartSeries) seriesObj;
                        String seriesName = series.getName();
                        Matcher matcher = chartParamPattern.matcher(seriesName);
                        while (matcher.find()) {
                            chartParams.add(matcher.group(1)); // x11, x12, x13等
                        }
                    }
                }
                
                // 如果发现了图表参数，尝试获取完整的图表数据
                if (!chartParams.isEmpty()) {
                    log.debug("发现图表参数: {}", chartParams);
                    // 方式一：查找通用的图表数据
                    Object chartData = data.get("chartData");
                    if (chartData != null) {
                        replaceCompleteChartData(chart, chartData);
                    } else {
                        // 方式二：基于参数构建图表数据
                        replaceChartWithParameterMapping(chart, data, chartParams);
                    }
                }
                
                // 处理图表其他位置的参数（如标题）
                replaceChartTextParameters(shape, data, chartParamPattern);
            }
        }
    }
    
    /**
     * 用完整的图表数据替换图表
     */
    private void replaceCompleteChartData(Chart chart, Object chartData) throws Exception {
        if (!(chartData instanceof Map)) {
            return;
        }
        
        Map<?, ?> data = (Map<?, ?>) chartData;
        Object categoriesObj = data.get("categories");
        Object seriesObj = data.get("series");
        
        if (categoriesObj instanceof java.util.List && seriesObj instanceof java.util.List) {
            java.util.List<?> categories = (java.util.List<?>) categoriesObj;
            java.util.List<?> seriesList = (java.util.List<?>) seriesObj;
            
            log.debug("替换完整图表数据，类别数: {}, 序列数: {}", categories.size(), seriesList.size());
            
            // 清空现有系列
            chart.getSeries().clear();
            
            String[] categoryArray = categories.toArray(new String[0]);
            for (int i = 0; i < categoryArray.length; i++) {
                categoryArray[i] = categories.get(i).toString();
            }
            
            // 添加新的数据系列
            for (Object seriesItem : seriesList) {
                if (seriesItem instanceof Map) {
                    Map<?, ?> series = (Map<?, ?>) seriesItem;
                    String name = (String) series.get("name");
                    java.util.List<?> values = (java.util.List<?>) series.get("values");
                    
                    if (values != null && name != null) {
                        double[] valueArray = new double[values.size()];
                        for (int i = 0; i < values.size(); i++) {
                            valueArray[i] = ((Number) values.get(i)).doubleValue();
                        }
                        
                        chart.getSeries().add(name, categoryArray, valueArray);
                    }
                }
            }
        }
    }
    
    /**
     * 基于参数映射替换图表
     */
    private void replaceChartWithParameterMapping(Chart chart, Map<String, Object> data, Set<String> chartParams) {
        // 这种方式用于当没有提供完整chartData时，仅替换序列名称
        for (int i = 0; i < chart.getSeries().getCount(); i++) {
            ChartSeries series = chart.getSeries().get(i);
            String seriesName = series.getName();
            
            Pattern pattern = Pattern.compile("\\{\\{(x\\d+)\\}\\}");
            Matcher matcher = pattern.matcher(seriesName);
            
            if (matcher.find()) {
                String paramName = matcher.group(1);
                Object value = data.get(paramName);
                if (value != null) {
                    String newName = formatValue(value);
                    series.setName(newName);
                    log.debug("替换图表序列名称: {} -> {}", paramName, newName);
                }
            }
        }
    }
    
    /**
     * 替换图表文本位置的参数
     */
    private void replaceChartTextParameters(Shape shape, Map<String, Object> data, Pattern pattern) {
        // 处理图表标题和其他文本元素中的参数
        String shapeText = shape.getText();
        
        if (pattern.matcher(shapeText).find()) {
            String newText = shapeText;
            Matcher matcher = pattern.matcher(shapeText);
            
            while (matcher.find()) {
                String paramName = matcher.group(1);
                Object value = data.get(paramName);
                String replacement = formatValue(value);
                newText = newText.replace(matcher.group(0), replacement);
            }
            
            // 注意：图表文本替换可能需要特殊处理，具体实现取决于Aspose.Words API
            log.debug("图表文本参数替换: {} -> {}", shapeText, newText);
        }
    }
    
    /**
     * 获取嵌套属性值
     */
    private Object getNestedValue(Map<String, Object> data, String path) {
        String[] parts = path.split("\\.");
        Object current = data;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 格式化值为字符串
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "";
        }
        
        if (value instanceof Number) {
            // 数字格式化
            DecimalFormat df = new DecimalFormat("#,##0.##");
            return df.format(value);
        }
        
        if (value instanceof Date) {
            // 日期格式化
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(value);
        }
        
        return value.toString();
    }
    
    /**
     * 将文档转换为字节数组
     */
    private byte[] documentToBytes(Document document) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        document.save(out, SaveFormat.DOCX);
        return out.toByteArray();
    }
}
package com.zjhh.economy.onlyoffice.manager;

import com.onlyoffice.manager.settings.DefaultSettingsManager;
import com.onlyoffice.model.settings.SettingsConstants;

import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2025/8/14 11:49
 */
public class SettingsManagerImpl extends DefaultSettingsManager {

    private final static Properties properties = new Properties();

    public SettingsManagerImpl(String docServerUrl, String securityKey) {
        properties.put(SettingsConstants.URL, docServerUrl);
        properties.put(SettingsConstants.SECURITY_KEY, securityKey);
        properties.put("customization.chat", "false");
        properties.put("customization.help", "false");
        properties.put("customization.forcesave", "true");
    }

    @Override
    public String getSetting(final String name) {
        return properties.getProperty(name);
    }

    @Override
    public void setSetting(final String name, final String value) {
        properties.setProperty(name, value);
    }
}

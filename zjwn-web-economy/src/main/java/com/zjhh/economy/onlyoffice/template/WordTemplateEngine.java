package com.zjhh.economy.onlyoffice.template;

import com.aspose.words.Document;
import com.zjhh.economy.onlyoffice.template.exception.TemplateProcessException;
import com.zjhh.economy.onlyoffice.template.filler.WordDocumentDataFiller;
import com.zjhh.economy.onlyoffice.template.model.DocumentGenerationResult;
import com.zjhh.economy.onlyoffice.template.model.TemplateFillData;
import com.zjhh.economy.onlyoffice.template.model.TemplateParseResult;
import com.zjhh.economy.onlyoffice.template.parser.WordTemplateParameterParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * Word模板参数替换引擎门面类
 * 提供统一的调用接口，封装参数解析和数据填充功能
 */
@Slf4j
@Component
public class WordTemplateEngine {

    private final WordTemplateParameterParser parameterParser;
    private final WordDocumentDataFiller dataFiller;

    public WordTemplateEngine() {
        this.parameterParser = new WordTemplateParameterParser();
        this.dataFiller = new WordDocumentDataFiller();
    }

    /**
     * 解析模板参数（保留用于参数分析）
     *
     * @param templateBytes 模板文件字节数组
     * @return 参数解析结果
     */
    public TemplateParseResult parseTemplate(byte[] templateBytes) {
        try {
            Document document = new Document(new ByteArrayInputStream(templateBytes));
            return parameterParser.parseTemplate(document);
        } catch (Exception e) {
            log.error("模板参数解析失败", e);
            throw new TemplateProcessException("模板参数解析失败", e);
        }
    }

    /**
     * 基于Model类解析并生成文档（推荐的类型安全接口）
     *
     * @param templateBytes 模板文件字节数组
     * @param fillData      Model类封装的填充数据
     * @return 生成结果，包含参数信息和文档内容
     */
    public DocumentGenerationResult parseAndGenerateWithModel(byte[] templateBytes, TemplateFillData fillData) {
        try {
            Document document = new Document(new ByteArrayInputStream(templateBytes));

            // 解析参数
            TemplateParseResult parseResult = parameterParser.parseTemplate(document);

            // 使用Model类进行数据填充
            Document fillDocument = new Document(new ByteArrayInputStream(templateBytes));
            byte[] documentBytes = dataFiller.fillDataWithModel(fillDocument, parseResult.getParameters(), fillData);
            return new DocumentGenerationResult(parseResult, documentBytes);
        } catch (Exception e) {
            log.error("基于Model类的解析模板并生成文档失败", e);
            throw new TemplateProcessException("基于Model类的解析模板并生成文档失败", e);
        }
    }

    /**
     * 基于Model类生成文档（InputStream版本）
     *
     * @param templateInputStream 模板文件输入流
     * @param fillData            Model类封装的填充数据
     * @return 生成的文档字节数组
     */
    public byte[] generateDocumentWithModel(InputStream templateInputStream, TemplateFillData fillData) {
        try {
            Document document = new Document(templateInputStream);

            // 解析参数
            TemplateParseResult parseResult = parameterParser.parseTemplate(document);

            // 重新加载文档进行数据填充
            Document fillDocument = new Document(templateInputStream);
            return dataFiller.fillDataWithModel(fillDocument, parseResult.getParameters(), fillData);
        } catch (Exception e) {
            log.error("基于Model类的文档生成失败", e);
            throw new TemplateProcessException("基于Model类的文档生成失败", e);
        }
    }

    /**
     * 基于Model类生成文档（byte[]版本）
     *
     * @param templateBytes 模板文件字节数组
     * @param fillData      Model类封装的填充数据
     * @return 生成的文档字节数组
     */
    public byte[] generateDocumentWithModel(byte[] templateBytes, TemplateFillData fillData) {
        try {
            Document document = new Document(new ByteArrayInputStream(templateBytes));

            // 解析参数
            TemplateParseResult parseResult = parameterParser.parseTemplate(document);

            // 重新加载文档进行数据填充
            Document fillDocument = new Document(new ByteArrayInputStream(templateBytes));
            return dataFiller.fillDataWithModel(fillDocument, parseResult.getParameters(), fillData);
        } catch (Exception e) {
            log.error("基于Model类的文档生成失败", e);
            throw new TemplateProcessException("基于Model类的文档生成失败", e);
        }
    }
}
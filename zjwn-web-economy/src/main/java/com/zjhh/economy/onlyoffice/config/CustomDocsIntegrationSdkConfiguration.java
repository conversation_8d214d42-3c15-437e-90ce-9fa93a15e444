package com.zjhh.economy.onlyoffice.config;

import com.onlyoffice.configuration.DocsIntegrationSdkConfiguration;
import com.onlyoffice.context.DocsIntegrationSdkContext;
import com.onlyoffice.manager.security.JwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.service.documenteditor.callback.CallbackService;
import com.onlyoffice.service.documenteditor.config.ConfigService;
import com.zjhh.economy.dao.mapper.AdsDocumentMapper;
import com.zjhh.economy.onlyoffice.manager.DocumentManagerImpl;
import com.zjhh.economy.onlyoffice.manager.SettingsManagerImpl;
import com.zjhh.economy.onlyoffice.manager.UrlManagerImpl;
import com.zjhh.economy.onlyoffice.service.CallbackServiceImpl;
import com.zjhh.economy.onlyoffice.service.ConfigServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/8/14 11:44
 */
@Configuration
public class CustomDocsIntegrationSdkConfiguration implements DocsIntegrationSdkConfiguration {

    @Resource
    private OnlyOfficeProperties onlyOfficeProperties;

    @Resource
    private AdsDocumentMapper adsDocumentMapper;

    @Resource
    private com.zjhh.system.service.DictService dictService;

    @Value("${server.servlet.context-path:/}")
    private String contextPath;

    @Override
    public SettingsManager settingsManager() {
        return new SettingsManagerImpl(onlyOfficeProperties.getOnlyOfficeUrl(), onlyOfficeProperties.getOnlyOfficeSecurityKey());
    }

    @Override
    public com.onlyoffice.manager.document.DocumentManager documentManager(final SettingsManager settingsManager) {
        return new DocumentManagerImpl(settingsManager, adsDocumentMapper);
    }

    @Override
    public com.onlyoffice.manager.url.UrlManager urlManager(final SettingsManager settingsManager) {
        return new UrlManagerImpl(settingsManager, onlyOfficeProperties.getServerUrl(), contextPath);
    }

    @Override
    public CallbackService callbackService(final JwtManager jwtManager, final SettingsManager settingsManager) {
        return new CallbackServiceImpl(jwtManager, settingsManager, adsDocumentMapper, dictService);
    }

    @Override
    public ConfigService configService(final com.onlyoffice.manager.document.DocumentManager documentManager, final com.onlyoffice.manager.url.UrlManager urlManager,
                                       final JwtManager jwtManager, final SettingsManager settingsManager) {
        return new ConfigServiceImpl(documentManager, urlManager, jwtManager, settingsManager);
    }

    @Bean
    DocsIntegrationSdkContext docsIntegrationSdkContext() {
        return new DocsIntegrationSdkContext(this);
    }
}

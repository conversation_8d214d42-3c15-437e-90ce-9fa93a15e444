package com.zjhh.economy.vo;

import com.zjhh.comm.vo.BaseVo;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.economy.dao.entity.AdsPmRoomOwnership;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/13 11:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomUpdateDetailVo extends BaseVo {

    private static final long serialVersionUID = 6909764686256735861L;

    @Schema(description = "房间id")
    private String roomId;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "楼宇id")
    private String buildingId;

    @Schema(description = "楼层id")
    private String floorId;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "建筑面积")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积")
    private BigDecimal businessArea;

    @Schema(description = "是否开放出租")
    private Boolean openHired;

    @Schema(description = "装修状态")
    private String renovationCode;

    @Schema(description = "房间类型")
    private String roomTypeCode;

    @Schema(description = "房间图片id列表")
    private List<String> roomImgIds;

    @Schema(description = "房间标签列表")
    private List<SingleSelectVo> roomLabels;

    @Schema(description = "租金单价")
    private BigDecimal rendUnitPrice;

    @Schema(description = "租金单价单位 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private Integer rendUnit;

    @Schema(description = "物业费单价 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private BigDecimal propertyFeesUnitPrice;

    @Schema(description = "物业费单价单位 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private Integer propertyFeesUnit;

    @Schema(description = "水费单价")
    private BigDecimal waterFeesUnitPrice;

    @Schema(description = "电费单价")
    private BigDecimal electricityFeesUnitPrice;

    @Schema(description = "房屋朝向 1-东 2-南 3-西 4-北 5-东南 6-西南 7-东北 8-西北 9-南北 10-东西")
    private Integer houseOrientation;

    @Schema(description = "权属性质")
    private String ownershipCode;

    @Schema(description = "产证状态")
    private String propertyCertificateCode;

    @Schema(description = "权属人")
    private List<AdsPmRoomOwnership> personList;
}

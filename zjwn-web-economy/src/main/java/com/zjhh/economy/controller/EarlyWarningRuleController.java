package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.onlyoffice.context.DocsIntegrationSdkContext;
import com.onlyoffice.manager.url.UrlManager;
import com.onlyoffice.model.documenteditor.Config;
import com.onlyoffice.model.documenteditor.config.document.Type;
import com.onlyoffice.model.documenteditor.config.editorconfig.Mode;
import com.onlyoffice.service.documenteditor.config.ConfigService;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.db.comm.Page;
import com.zjhh.economy.dao.entity.AdsDmsReport;
import com.zjhh.economy.dao.entity.AdsDmsReportLog;
import com.zjhh.economy.request.earlywarning.*;
import com.zjhh.economy.service.EarlyWarningRuleService;
import com.zjhh.economy.vo.earlywarning.AdsDmsReportVo;
import com.zjhh.economy.vo.earlywarning.AlysReportPageVo;
import com.zjhh.system.service.DictService;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/19 14:33
 */
@Slf4j
@Tag(name = "监督预警")
@ApiSupport(order = 1201)
@RestController
@SaCheckLogin
@RequestMapping("early/warning/rule")
public class EarlyWarningRuleController extends BaseController {

    @Resource
    private EarlyWarningRuleService earlyWarningRuleService;

    @Resource
    private DictService dictService;

    @Resource
    private DocsIntegrationSdkContext docsIntegrationSdk;

    @Operation(summary = "分析报告模板列表")
    @PostMapping("page/analysis/report")
    public ReData<Page<AdsDmsReportVo>> pageAnalysisReport(@RequestBody @Validated PageAnalysisReportReq req) {
        return ReData.success(earlyWarningRuleService.pageAnalysisReport(req));
    }

    @Operation(summary = "新增分析报告模板")
    @PostMapping("add/alys/report")
    public ReData<String> addAlysReport(@RequestBody @Validated AddAdsDmsReportReq req) {
        earlyWarningRuleService.addAlysReport(req);
        return ReData.success();
    }

    @Operation(summary = "判断分析报告模板编码是否存在")
    @PostMapping("check/alys/report/code")
    public ReData<Boolean> checkAlysReportCode(@RequestBody @Validated ReportTypeCodeReq req) {
        return ReData.success(earlyWarningRuleService.checkAlysReportCode(req.getReportTypeCode()));
    }

    @Operation(summary = "批量删除分析报告模板")
    @PostMapping("del/alys/report")
    public ReData<String> delAlysReport(@RequestBody List<String> reportTypeCodes) {
        earlyWarningRuleService.delAlysReport(reportTypeCodes);
        return ReData.success();
    }

    @Operation(summary = "编辑分析报告模板")
    @PostMapping("edit/alys/report")
    public ReData<String> editAlysReport(@RequestBody @Validated AddAdsDmsReportReq req) {
        earlyWarningRuleService.editAlysReport(req);
        return ReData.success();
    }

    @Operation(summary = "分析报告日志列表")
    @PostMapping("page/alys/report/log")
    public ReData<Page<AdsDmsReportLog>> pageAlysReportLog(@RequestBody @Validated PageAlysReportLogReq req) {
        return ReData.success(earlyWarningRuleService.pageAlysReportLog(req));
    }

    @Operation(summary = "获取分析报告日志详情")
    @PostMapping("get/alys/report/log/info")
    public ReData<AdsDmsReportLog> getAlysReportLogInfo(@RequestBody String id) {
        if (ObjectUtil.isEmpty(id)) {
            throw new BizException("日志id不能为空！");
        }
        return ReData.success(earlyWarningRuleService.getAlysReportLogInfo(id));
    }

    @Operation(summary = "获取分析报告数据集")
    @PostMapping("get/data/set")
    public ReData<List<AddAlysReportDataSetReq>> getDataSet(@RequestBody @Validated ReportTypeCodeReq req) {
        return ReData.success(earlyWarningRuleService.getDataSet(req.getReportTypeCode()));
    }

    @Operation(summary = "添加数据源")
    @PostMapping("add/data/set")
    public ReData<String> addDataSet(@RequestBody @Validated AddAlysReportDataSetReq req) {
        earlyWarningRuleService.addDataSet(req);
        return ReData.success();
    }

    @Operation(summary = "编辑数据源")
    @PostMapping("edit/data/set")
    public ReData<String> editDataSet(@RequestBody @Validated UpdateAlysReportDataSetReq req) {
        earlyWarningRuleService.editDataSet(req);
        return ReData.success();
    }

    @Operation(summary = "删除数据源")
    @PostMapping("del/data/set")
    public ReData<String> delDataSet(@RequestBody String dataSetId) {
        if (ObjectUtil.isEmpty(dataSetId)) {
            throw new BizException("数据源id不能为空！");
        }
        earlyWarningRuleService.delDataSet(dataSetId);
        return ReData.success();
    }

    @Operation(summary = "获取分析报告界面")
    @PostMapping("get/alys/report/page")
    public ReData<AlysReportPageVo> getAlysReportPage(@RequestBody @Validated ReportTypeCodeReq req) {
        AdsDmsReport report = earlyWarningRuleService.getAlysReportPage(req.getReportTypeCode());
        ConfigService configService = docsIntegrationSdk.getConfigService();
        UrlManager urlManager = docsIntegrationSdk.getUrlManager();

        Config config = configService.createConfig(report.getDocId(), Mode.EDIT, Type.DESKTOP);
        config.getEditorConfig().setLang("zh");
        AlysReportPageVo vo = new AlysReportPageVo();
        vo.setConfig(config);
        vo.setDocumentServerApiUrl(urlManager.getDocumentServerApiUrl());
        return ReData.success(vo);
    }

    @Operation(summary = "下载分析报告模板制作说明书")
    @PostMapping("temp/manual/download")
    public ResponseEntity<org.springframework.core.io.Resource> download() {
        try {
            String fileName = "《分析报告模板制作说明》.docx";
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("template/reportCreateTmp.docx");

            if (inputStream == null) {
                throw new BizException("说明书文件不存在！");
            }

            // 创建临时文件资源
            org.springframework.core.io.Resource resource = new InputStreamResource(inputStream);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=" + URLEncodeUtil.encode(fileName, StandardCharsets.UTF_8))
                    .body(resource);

        } catch (Exception e) {
            log.error("下载分析报告模板制作说明书失败", e);
            throw new BizException("下载失败！");
        }
    }

    @Operation(summary = "获取可选择的行政区划")
    @PostMapping("list/select/org/codes")
    public ReData<List<TreeSelectVo>> listSelectOrgCodes() {
        return ReData.success(earlyWarningRuleService.listSelectOrgCodes());
    }

    @Operation(summary = "保存分析报告界面")
    @PostMapping("save/alys/report/page")
    public ReData<String> saveAlysReportPage(@RequestBody @Validated SaveAlysReportPageReq req) {
        earlyWarningRuleService.saveAlysReportPage(req);
        return ReData.success();
    }

    @Operation(summary = "保存和预览分析报告界面")
    @PostMapping("save_pre/alys/report/page")
    public ReData<AlysReportPageVo> saveAndPreviewAlysReport(@RequestBody @Validated SaveAndPreviewAlysReportReq req) {
        String docId = earlyWarningRuleService.saveAndPreviewAlysReport(req);
        ConfigService configService = docsIntegrationSdk.getConfigService();
        UrlManager urlManager = docsIntegrationSdk.getUrlManager();

        Config config = configService.createConfig(docId, Mode.VIEW, Type.DESKTOP);
        config.getEditorConfig().setLang("zh");
        AlysReportPageVo vo = new AlysReportPageVo();
        vo.setConfig(config);
        vo.setDocumentServerApiUrl(urlManager.getDocumentServerApiUrl());
        return ReData.success(vo);
    }

    @Operation(summary = "判断预警分析报告模板是否存在")
    @PostMapping("check/report/doc/exist")
    public ReData<Boolean> checkReportDocExist(@RequestBody @Validated CreateReportDocReq req) {
        return ReData.success(earlyWarningRuleService.checkReportDocExist(req));
    }

    @Operation(summary = "预警分析报告生成")
    @PostMapping("create/report/doc")
    public ReData<String> createReportDoc(@RequestBody @Validated CreateReportDocReq req) {
        req.setType(2);
        earlyWarningRuleService.createReportDoc(req);
        return ReData.success();
    }

    @Operation(summary = "预览预警分析报告")
    @PostMapping("preview/report/doc")
    public ReData<AlysReportPageVo> previewReportDoc(@RequestBody @Validated PreviewReportDocReq req) {
        String docId = earlyWarningRuleService.previewReportDoc(req);
        ConfigService configService = docsIntegrationSdk.getConfigService();
        UrlManager urlManager = docsIntegrationSdk.getUrlManager();

        Config config = configService.createConfig(docId, Mode.VIEW, Type.DESKTOP);
        config.getEditorConfig().setLang("zh");
        AlysReportPageVo vo = new AlysReportPageVo();
        vo.setConfig(config);
        vo.setDocumentServerApiUrl(urlManager.getDocumentServerApiUrl());
        return ReData.success(vo);
    }
}

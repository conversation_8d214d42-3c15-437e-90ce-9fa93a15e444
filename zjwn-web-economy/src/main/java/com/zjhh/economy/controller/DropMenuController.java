package com.zjhh.economy.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.util.ObjectUtil;
import com.github.xingfudeshi.knife4j.annotations.ApiOperationSupport;
import com.github.xingfudeshi.knife4j.annotations.ApiSupport;
import com.zjhh.comm.request.IdReq;
import com.zjhh.comm.response.ReData;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.comm.vo.TreeSelectVo;
import com.zjhh.economy.request.MoreParamsReq;
import com.zjhh.economy.request.TargetPageReq;
import com.zjhh.economy.request.analyzecockpit.CommunityDropMenuReq;
import com.zjhh.economy.request.analyzereport.ChooseFilterReq;
import com.zjhh.economy.service.DropMenuService;
import com.zjhh.economy.vo.CockpitTreeSelectedVo;
import com.zjhh.economy.vo.DocDateVo;
import com.zjhh.economy.vo.SettleRoomMenuVo;
import com.zjhh.user.request.CodeReq;
import com.zjhh.web.base.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/11 16:11
 */
@Slf4j
@Tag(name = "下拉选择")
@ApiSupport(order = 1002)
@SaCheckLogin
@RestController
@RequestMapping("drop/menu")
public class DropMenuController extends BaseController {

    @Resource
    private DropMenuService dropMenuService;

    @Operation(summary = "项目选择下拉")
    @ApiOperationSupport(order = 1)
    @PostMapping("list/project")
    public ReData<List<SingleSelectVo>> listProject(@Validated @RequestBody(required = false) CodeReq req) {
        return ReData.success(dropMenuService.listProject(ObjectUtil.isNotNull(req) ? req.getCode() : null));
    }

    @Operation(summary = "社区选择下拉")
    @ApiOperationSupport(order = 2)
    @PostMapping("list/community")
    public ReData<List<SingleSelectVo>> listCommunity() {
        return ReData.success(dropMenuService.listCommunity());
    }

    @Operation(summary = "权属下拉")
    @ApiOperationSupport(order = 3)
    @PostMapping("list/ownership")
    public ReData<List<SingleSelectVo>> listOwnership() {
        return ReData.success(dropMenuService.listOwnership());
    }

    @Operation(summary = "楼宇状态下拉")
    @ApiOperationSupport(order = 4)
    @PostMapping("list/building/status")
    public ReData<List<SingleSelectVo>> listBuildingStatus() {
        return ReData.success(dropMenuService.listBuildingStatus());
    }

    @Operation(summary = "楼宇类型")
    @ApiOperationSupport(order = 5)
    @PostMapping("list/building/type")
    public ReData<List<SingleSelectVo>> listBuildingType() {
        return ReData.success(dropMenuService.listBuildingType());
    }

    @Operation(summary = "楼宇标签")
    @ApiOperationSupport(order = 6)
    @PostMapping("list/building/label")
    public ReData<List<SingleSelectVo>> listBuildingLabel() {
        return ReData.success(dropMenuService.listBuildingLabel());
    }

    @Operation(summary = "楼宇配套")
    @ApiOperationSupport(order = 7)
    @PostMapping("list/building/config")
    public ReData<List<SingleSelectVo>> listBuildingConfig() {
        return ReData.success(dropMenuService.listBuildingConfig());
    }

    @Operation(summary = "楼宇定位")
    @ApiOperationSupport(order = 8)
    @PostMapping("list/building/position")
    public ReData<List<TreeSelectVo>> listBuildingPosition() {
        return ReData.success(dropMenuService.listBuildingPosition());
    }

    @Operation(summary = "装修状态")
    @ApiOperationSupport(order = 9)
    @PostMapping("list/renovation")
    public ReData<List<SingleSelectVo>> listRenovation() {
        return ReData.success(dropMenuService.listRenovation());
    }

    @Operation(summary = "房间类型")
    @ApiOperationSupport(order = 10)
    @PostMapping("list/room/type")
    public ReData<List<SingleSelectVo>> listRoomType() {
        return ReData.success(dropMenuService.listRoomType());
    }

    @Operation(summary = "产证状态")
    @ApiOperationSupport(order = 11)
    @PostMapping("list/property/certificate")
    public ReData<List<SingleSelectVo>> listPropertyCertificate() {
        return ReData.success(dropMenuService.listPropertyCertificate());
    }

    @Operation(summary = "房间标签")
    @ApiOperationSupport(order = 12)
    @PostMapping("list/room/label")
    public ReData<List<SingleSelectVo>> listRoomLabel() {
        return ReData.success(dropMenuService.listRoomLabel());
    }

    @Operation(summary = "楼宇下拉")
    @ApiOperationSupport(order = 13)
    @PostMapping("list/building")
    public ReData<List<SingleSelectVo>> listBuilding(@Validated @RequestBody(required = false) IdReq req) {
        return ReData.success(dropMenuService.listBuilding(ObjectUtil.isNotNull(req) ? req.getId() : null));
    }

    @Operation(summary = "楼层下拉")
    @ApiOperationSupport(order = 14)
    @PostMapping("list/floor")
    public ReData<List<SingleSelectVo>> listFloor(@Validated @RequestBody(required = false) IdReq req) {
        return ReData.success(dropMenuService.listFloor(ObjectUtil.isNotNull(req) ? req.getId() : null));
    }

    @Operation(summary = "行业下拉")
    @ApiOperationSupport(order = 15)
    @PostMapping("list/industry")
    public ReData<List<TreeSelectVo>> listIndustryTree() {
        return ReData.success(dropMenuService.listIndustry());
    }

    @Operation(summary = "企业标签")
    @ApiOperationSupport(order = 16)
    @PostMapping("list/ent/label")
    public ReData<List<SingleSelectVo>> listEntLabel() {
        return ReData.success(dropMenuService.listEntLabel());
    }

    @Operation(summary = "企业类型")
    @ApiOperationSupport(order = 17)
    @PostMapping("list/ent/type")
    public ReData<List<TreeSelectVo>> listEntType() {
        return ReData.success(dropMenuService.listEntType());
    }

    @Operation(summary = "融资阶段")
    @ApiOperationSupport(order = 18)
    @PostMapping("list/financing/stage")
    public ReData<List<SingleSelectVo>> listFinancingStage() {
        return ReData.success(dropMenuService.listFinancingStage());
    }


    @Operation(summary = "日期")
    @ApiOperationSupport(order = 19)
    @PostMapping("get/date")
    public ReData<DocDateVo> getDocumentDate(@RequestBody TargetPageReq req) {
        return ReData.success(dropMenuService.getDate(req.getTargetPage()));
    }

    @Operation(summary = "房间下拉框")
    @ApiOperationSupport(order = 20)
    @PostMapping("list/room")
    public ReData<List<SettleRoomMenuVo>> listRoom(@Validated @RequestBody(required = false) IdReq req) {
        return ReData.success(dropMenuService.listRoom(req.getId()));
    }

    @Operation(summary = "项目类型")
    @ApiOperationSupport(order = 21)
    @PostMapping("list/projectType")
    public ReData<List<SingleSelectVo>> listProjectType() {
        return ReData.success(dropMenuService.listProjectType());
    }


    @Operation(summary = "行业4级下拉")
    @ApiOperationSupport(order = 22)
    @PostMapping("list/industry/level4")
    public ReData<List<TreeSelectVo>> listIndustryTreeLevel4() {
        return ReData.success(dropMenuService.listIndustryByLevel4());
    }


    @Operation(summary = "社区项目下拉")
    @ApiOperationSupport(order = 23)
    @PostMapping("list/community/building")
    public ReData<List<TreeSelectVo>> listCommunityBuilding() {
        return ReData.success(dropMenuService.listCommunityBuilding());
    }

    @Operation(summary = "大屏街道下拉框")
    @ApiOperationSupport(order = 23)
    @PostMapping("list/community/building/cockpit")
    public ReData<List<CockpitTreeSelectedVo>> listCommunityBuildingCockpit() {
        CommunityDropMenuReq req = new CommunityDropMenuReq();
        req.setProjectType("");
        return ReData.success(dropMenuService.listCommunityBuildingCockpit(req));
    }


    @Operation(summary = "社区项目下拉-没有楼层")
    @ApiOperationSupport(order = 25)
    @PostMapping("list/community/building/tax")
    public ReData<List<TreeSelectVo>> listCommunityBuildingWithoutFloor() {
        return ReData.success(dropMenuService.listCommunityBuildingWithoutFloor());
    }

    @Operation(summary = "行业3级下拉")
    @ApiOperationSupport(order = 26)
    @PostMapping("list/industry/level3")
    public ReData<List<TreeSelectVo>> listIndustryTreeLevel3() {
        return ReData.success(dropMenuService.listIndustryByLevel3(3));
    }

    @Operation(summary = "项目类型-无其他")
    @ApiOperationSupport(order = 27)
    @PostMapping("list/projectType/noElse")
    public ReData<List<SingleSelectVo>> listProjectTypeNoElse() {
        return ReData.success(dropMenuService.listProjectTypeNoElse());
    }

    @Operation(summary = "社区项目楼宇筛选器")
    @ApiOperationSupport(order = 28)
    @PostMapping("list/filter")
    public ReData<List<TreeSelectVo>> listFilter(@Validated @RequestBody ChooseFilterReq req) {
        return ReData.success(dropMenuService.listChooseFilter(req));
    }


    @Operation(summary = "项目选择下拉-多选参数")
    @ApiOperationSupport(order = 29)
    @PostMapping("list/more/project")
    public ReData<List<SingleSelectVo>> listMoreProject(@Validated @RequestBody(required = false) MoreParamsReq req) {
        return ReData.success(dropMenuService.listMoreProject(ObjectUtil.isNotNull(req) ? req.getCodes() : null));
    }

    @Operation(summary = "楼宇下拉-多选参数")
    @ApiOperationSupport(order = 30)
    @PostMapping("list/more/building")
    public ReData<List<SingleSelectVo>> listMoreBuilding(@Validated @RequestBody(required = false) MoreParamsReq req) {
        return ReData.success(dropMenuService.listMoreBuilding(ObjectUtil.isNotNull(req) ? req.getCodes() : null));
    }


    @Operation(summary = "行业4级下拉-除第一产业")
    @ApiOperationSupport(order = 31)
    @PostMapping("list/industry/level4Without")
    public ReData<List<TreeSelectVo>> listIndustryTreeLevel3Without() {
        return ReData.success(dropMenuService.listIndustryByLevel3(4));
    }


    @Operation(summary = "移动街道下拉框")
    @ApiOperationSupport(order = 32)
    @PostMapping("list/community/building/cockpit/app")
    public ReData<List<CockpitTreeSelectedVo>> listCommunityBuildingCockpitApp(@RequestBody CommunityDropMenuReq req) {
        req.setProjectType("ly");
        return ReData.success(dropMenuService.listCommunityBuildingCockpit(req));
    }


    @Operation(summary = "提交途径")
    @ApiOperationSupport(order = 33)
    @PostMapping("list/submit/source")
    public ReData<List<SingleSelectVo>> listSubmitSource() {
        return ReData.success(dropMenuService.listSubmitSource());
    }

    @Operation(summary = "诉求类型")
    @ApiOperationSupport(order = 34)
    @PostMapping("list/demand/type")
    public ReData<List<SingleSelectVo>> listDemandType() {
        return ReData.success(dropMenuService.listDemandType());
    }

    @Operation(summary = "全部项目选择下拉")
    @ApiOperationSupport(order = 35)
    @PostMapping("list/all/project")
    public ReData<List<SingleSelectVo>> listAllProject() {
        return ReData.success(dropMenuService.listProject(null));
    }

    @Operation(summary = "全部楼宇下拉")
    @ApiOperationSupport(order = 36)
    @PostMapping("list/all/building")
    public ReData<List<SingleSelectVo>> listAllBuilding() {
        return ReData.success(dropMenuService.listBuilding(null));
    }

    @Operation(summary = "迁出原因")
    @ApiOperationSupport(order = 37)
    @PostMapping("list/move/label")
    public ReData<List<SingleSelectVo>> listMoveLabel() {
        return ReData.success(dropMenuService.listMoveLabel());
    }


}

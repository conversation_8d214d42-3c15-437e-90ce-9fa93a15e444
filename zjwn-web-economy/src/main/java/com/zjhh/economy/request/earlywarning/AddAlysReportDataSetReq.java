package com.zjhh.economy.request.earlywarning;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/11 15:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddAlysReportDataSetReq extends BaseReq {

    private static final long serialVersionUID = 4319917197260532054L;

    @Schema(description = "分析报告模板编码")
    private String reportTypeCode;

    @Schema(description = "业务数据编码")
    private String dataTypeCode;

    @Schema(description = "业务数据名称")
    private String dataSetName;

    @Schema(description = "业务数据Id")
    private String dataSetId;

    private String key;

    private String parentKey;

    private String title;

    @Schema(description = "字段信息")
    private List<AddAlysReportDataColumnSetReq> children;
}

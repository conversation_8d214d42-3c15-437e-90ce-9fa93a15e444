package com.zjhh.economy.request;

import com.zjhh.comm.request.BaseReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/8 17:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddRoomReq extends BaseReq {

    private static final long serialVersionUID = -4731272738446833182L;

    @Schema(description = "项目id")
    @NotBlank(message = "项目id不能为空！")
    private String projectId;

    @Schema(description = "楼宇id")
    @NotBlank(message = "楼宇id不能为空！")
    private String buildingId;

    @Schema(description = "楼层id")
    @NotBlank(message = "楼层id不能为空！")
    private String floorId;

    @Schema(description = "房号")
    @NotBlank(message = "房号不能为空！")
    private String roomNo;

    @Schema(description = "建筑面积")
    @NotNull(message = "建筑面积不能为空！")
    private BigDecimal buildingArea;

    @Schema(description = "商务面积")
    @NotNull(message = "商务面积不能为空！")
    private BigDecimal businessArea;

    @Schema(description = "是否开放出租")
    @NotNull(message = "是否开放出租不能为空！")
    private Boolean openHired;

    @Schema(description = "装修状态")
    private String renovationCode;

    @Schema(description = "房间类型")
    private String roomTypeCode;

    @Schema(description = "房间图片id列表")
    private List<String> roomImgIds;

    @Schema(description = "房间标签列表")
    private List<String> roomLabelCodes;

    @Schema(description = "租金单价")
    private BigDecimal rendUnitPrice;

    @Schema(description = "租金单价单位 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private Integer rendUnit;

    @Schema(description = "物业费单价 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private BigDecimal propertyFeesUnitPrice;

    @Schema(description = "物业费单价单位 1-元/㎡/天 2-元/㎡/月 3-元/月")
    private Integer propertyFeesUnit;

    @Schema(description = "水费单价")
    private BigDecimal waterFeesUnitPrice;

    @Schema(description = "电费单价")
    private BigDecimal electricityFeesUnitPrice;

    @Schema(description = "房屋朝向 1-东 2-南 3-西 4-北 5-东南 6-西南 7-东北 8-西北 9-南北 10-东西")
    private Integer houseOrientation;

    @Schema(description = "权属性质")
    @NotBlank(message = "权属性质不能为空！")
    private String ownershipCode;

    @Schema(description = "产证状态")
    @NotBlank(message = "产证状态不能为空！")
    private String propertyCertificateCode;

    @Schema(description = "权属人")
    @Valid
    private List<AddRoomPersonReq> personList;

    @Schema(description = "操作位置。1-房源管理，2-楼宇管理")
    private String optType;
}
